/**
 * Analysis Orchestrator
 *
 * This module orchestrates the analysis process by coordinating the execution
 * of various analysis modules and assembling the final result.
 */

import { ParsedMessage } from '../parser';
import { CloudflareBindings } from '../types';
import { getLlmResult } from '../utils/resultHelpers';
import { manageChatCache, getCacheStatus } from './llm';

// Import analysis types configuration
import { getAnalysisTypeConfig, shouldRunTaskForAnalysisType } from './config/analysisTypes';

// Import from the modular analysis structure
import {
  // Core analysis
  analyzeActivity, calculateAverageResponseTime,
  analyzeSentiment,
  analyzeTopics,

  // Social analysis
  calculatePairwiseSwitches,

  // Content analysis
  analyzeMediaStats,

  // Metrics analysis
  analyzeParticipantMetrics,
  calculateConversationStats,

  // LLM analysis
  LlmBatchTask, LlmTaskResponse,
  runLlmBatchTasks,

  // Utils
  preprocessMessages
} from './index';

// Import character classification module
import { classifyCharacters } from './characterClassification';

// Import the personality index calculator
import { calculatePersonalityIndexes } from './metrics/personalityIndex';

/**
 * Note: The following frontend features are implemented but don't have backend counterparts yet:
 * - PassiveAggressiveAwards: Currently uses mock data in the frontend
 * - MoodSwings: Currently uses mock data in the frontend
 * - DramaOMeter/CuteOMeter: Currently use mock data in the frontend
 *
 * These features could be implemented as LLM tasks in the future.
 */

/**
 * Discovers all participants in the chat with their message counts
 * Used by frontend to show participant selection UI
 *
 * @param parsedMessages The parsed messages to analyze
 * @returns Array of participants with their message counts, sorted by activity
 */
export async function getAvailableParticipants(
  parsedMessages: ParsedMessage[]
): Promise<{id: string, name: string, messageCount: number}[]> {
  console.log(`Discovering participants from ${parsedMessages.length} messages...`);
  
  // Preprocess messages to get participant data
  const preprocessedData = preprocessMessages(parsedMessages);
  console.log(`Found ${preprocessedData.participants.length} total participants`);
  
  // Count messages per participant
  const messageCounts = preprocessedData.participants.reduce((acc, name) => {
    acc[name] = preprocessedData.userMessages.filter(msg => msg.sender === name).length;
    return acc;
  }, {} as Record<string, number>);
  
  // Filter out "You" entries and create participant objects with message counts
  const availableParticipants = preprocessedData.participants
    .filter(name => name !== "You" && name !== "\u200EYou") // Filter out "You" and "‎You" (with U+200E)
    .map((name, i) => ({
      id: `p${i+1}`,
      name,
      messageCount: messageCounts[name] || 0
    }))
    .sort((a, b) => b.messageCount - a.messageCount); // Sort by message count (most active first)
  
  console.log(`Returning ${availableParticipants.length} available participants for selection`);
  console.log(`Top participants: ${availableParticipants.slice(0, 3).map(p => `${p.name} (${p.messageCount} messages)`).join(', ')}`);
  
  return availableParticipants;
}

/**
 * Performs full analysis on parsed messages
 *
 * @param parsedMessages The parsed messages to analyze
 * @param fileName The name of the chat file
 * @param analysisId The unique ID for this analysis
 * @param env The Cloudflare environment bindings
 * @param selectedLlmTaskIds Optional array of task IDs to run
 * @param analysisType Optional analysis type (chat, love, etc.)
 * @returns The analysis result
 */
export async function performFullAnalysis(
  parsedMessages: ParsedMessage[],
  fileName: string,
  analysisId: string,
  env: CloudflareBindings,
  selectedLlmTaskIds?: string[],
  analysisType?: string, // New parameter for analysis type
  selectedParticipants?: string[] // For love analysis: array of exactly 2 participant names
): Promise<any> {
  console.log(`Starting analysis for ID: ${analysisId} with ${parsedMessages.length} messages...`);
  if (selectedLlmTaskIds) {
    console.log("Orchestrator received selected LLM Task IDs:", selectedLlmTaskIds);
  }

  // --- Preprocess Messages ---
  console.log("Preprocessing messages...");
  const preprocessedData = preprocessMessages(parsedMessages);
  console.log(`Preprocessing complete. Found ${preprocessedData.totalUserMessages} user messages from ${preprocessedData.participants.length} participants.`);

  // Create a filtered version of allMessages that excludes messages with sender "System"
  const allMessagesExceptSystem = preprocessedData.allMessages.filter(msg =>
    msg.sender.toLowerCase() !== 'system'
  );
  console.log(`Filtered out ${preprocessedData.allMessages.length - allMessagesExceptSystem.length} messages with sender "System"`);

  // --- Determine Participants and Analysis Type ---
  const initialParticipantsListWithIds = preprocessedData.participants
                                  // Filter out "You" and "‎You" (with U+200E)
                                  .filter(name => name !== "You" && name !== "\u200EYou")
                                  .map((sender, i) => ({ id: `p${i+1}`, name: sender }));

  const participantCount = initialParticipantsListWithIds.length;



  // Determine analysis type based on parameter or participant count
  let detectedAnalysisType = participantCount === 2 ? 'personal' : 'group';

  // Override with provided analysis type if available
  if (analysisType) {
    console.log(`Using provided analysis type: ${analysisType}`);
    detectedAnalysisType = analysisType;
  } else {
    console.log(`Auto-detected analysis type: ${detectedAnalysisType} based on ${participantCount} participants`);
  }

  // Get the analysis type configuration
  const analysisTypeConfig = getAnalysisTypeConfig(detectedAnalysisType);
  console.log(`Using analysis type: ${analysisTypeConfig.name} (${analysisTypeConfig.id})`);

  // --- Run Common Analysis Modules in Parallel ---
  console.log("Running analysis modules in parallel...");

  // Define all analysis tasks to run in parallel
  const analysisPromises = {
    activity: Promise.resolve().then(() => analyzeActivity(preprocessedData.userMessages)),
    sentiment: Promise.resolve().then(() => analyzeSentiment(preprocessedData.userMessages)),
    topics: Promise.resolve().then(() => analyzeTopics(preprocessedData.userMessages)),
    mediaStats: Promise.resolve().then(() => analyzeMediaStats(allMessagesExceptSystem)), // Use filtered allMessages
    avgResponseTime: Promise.resolve().then(() => calculateAverageResponseTime(preprocessedData.userMessages))
  };

  // Wait for all analysis tasks to complete
  const [
    activityResult,
    sentimentResult,
    topicResult,
    mediaStatsResult,
    avgResponseTimeString
  ] = await Promise.all([
    analysisPromises.activity,
    analysisPromises.sentiment,
    analysisPromises.topics,
    analysisPromises.mediaStats,
    analysisPromises.avgResponseTime
  ]);

  console.log("All parallel analysis tasks completed!");

  // Augment messages with sentiment scores for participant metrics calculation
  const analyzedMessages = preprocessedData.userMessages.map(msg => ({
      ...msg,
      sentimentScore: sentimentResult.sentimentPerMessageMap.get(msg)
  }));

  // Run dependent analyses that need the sentiment-augmented messages
  console.log("Running dependent analyses that require sentiment scores...");

  // Run these two analyses in parallel as they both depend on analyzedMessages
  const [participantMetricsResult, conversationStatsResult] = await Promise.all([
    Promise.resolve().then(() => analyzeParticipantMetrics(analyzedMessages, initialParticipantsListWithIds)),
    Promise.resolve().then(() => calculateConversationStats(analyzedMessages))
  ]);

  // Calculate personality indexes
  const personalityIndexes = await calculatePersonalityIndexes(parsedMessages);
  console.log(`Calculated personality indexes for ${Object.keys(personalityIndexes).length} participants`);

  // --- LLM Batch Analysis Setup ---
  const batchTasks: LlmBatchTask[] = [];
  let analysisResult: any = {}; // Define a more specific type later based on mockAnalysisData.json
  let batchResults: Record<string, LlmTaskResponse> = {};

  // Initialize token usage tracking (structure remains similar)
  const tokenUsage = {
    totalInputTokens: 0,
    totalOutputTokens: 0,
    totalCachedTokens: 0,
    totalTokens: 0,
    inputCost: 0,
    outputCost: 0,
    cachingCost: 0,
    storageCost: 0,
    totalCost: 0,
    taskBreakdown: [] as Array<{
      task: string,
      inputTokens: number,
      outputTokens: number,
      cachedTokens?: number,
      totalTokens: number,
      inputCost?: number,
      outputCost?: number,
      cachingCost?: number,
      storageCost?: number,
      cost: number
    }>,
    // Add a method to update token usage from a task result
    addTaskUsage: function(taskName: string, result: any) {
      if (result && result.tokenUsage) {
        const usage = result.tokenUsage;
        this.totalInputTokens += usage.inputTokens || 0;
        this.totalOutputTokens += usage.outputTokens || 0;
        this.totalCachedTokens += usage.cachedContentTokenUsage || usage.cachedTokens || 0;
        this.totalTokens += usage.totalTokens || 0;

        // Use the costs already calculated by llmHelper
        const taskInputCost = usage.inputCost || 0;
        const taskOutputCost = usage.outputCost || 0;
        const taskCachingCost = usage.cachingCost || 0;
        const taskStorageCost = usage.storageCost || 0;
        const taskCost = usage.cost || 0;

        // Accumulate costs
        this.inputCost += taskInputCost;
        this.outputCost += taskOutputCost;
        this.cachingCost += taskCachingCost;
        this.storageCost += taskStorageCost;
        this.totalCost += taskCost;

        this.taskBreakdown.push({
          task: taskName,
          inputTokens: usage.inputTokens || 0,
          outputTokens: usage.outputTokens || 0,
          cachedTokens: usage.cachedContentTokenUsage || usage.cachedTokens || 0,
          totalTokens: usage.totalTokens || 0,
          inputCost: taskInputCost,
          outputCost: taskOutputCost,
          cachingCost: taskCachingCost,
          storageCost: taskStorageCost,
          cost: taskCost
        });

        // Simplified token usage logging
        console.log(`Added token usage for task "${taskName}": ${usage.totalTokens || 0} tokens, cost: $${taskCost.toFixed(6)}`);
      }
    }
  };

  // --- Token Cost Limitation Setup (Synchronized with api.ts) ---
  const MAX_INPUT_TOKENS = 100000; // Same as api.ts MAX_INPUT_TOKENS

  // Function to estimate tokens using api.ts logic (4 chars per token)
  const estimateTokens = (messages: any[], participants: any[] = []) => {
    const messageText = messages.map((msg, index) =>
      `${index}: [${msg.timestamp?.toISOString() || 'No Timestamp'}] ${msg.sender}: ${msg.text}`
    ).join('\n');
    const participantText = `Participants: ${participants?.map(p => `${p.name} (ID: ${p.id})`).join(', ') || 'Unknown'}`;
    const systemText = "You are a chat analysis assistant. Analyze the following chat messages provided.";
    const fullPrompt = `${systemText}\n${participantText}\nMessages:\n${messageText}`;
    return Math.ceil(fullPrompt.length / 4); // Same as api.ts: 4 chars per token
  };



  // Function to filter messages using api.ts-compatible logic
  const filterMessagesByTokens = (messages: any[], participants: any[] = []) => {
    if (messages.length === 0) return messages;

    // Estimate tokens for all messages
    let estimatedTokens = estimateTokens(messages, participants);

    // If within token limit, return all messages
    if (estimatedTokens <= MAX_INPUT_TOKENS) {
      console.log(`All ${messages.length} messages fit within token limit. Estimated tokens: ${estimatedTokens}`);
      return messages;
    }

    console.log(`Messages exceed ${MAX_INPUT_TOKENS} token limit (${estimatedTokens} estimated tokens). Applying api.ts-compatible truncation...`);

    // Apply api.ts-style truncation: iteratively remove 10% from start
    let selectedMessages = [...messages];
    let currentTokens = estimatedTokens;

    while (currentTokens > MAX_INPUT_TOKENS && selectedMessages.length > 100) {
      const removeCount = Math.ceil(selectedMessages.length * 0.1); // Remove 10% from start
      selectedMessages = selectedMessages.slice(removeCount);
      currentTokens = estimateTokens(selectedMessages, participants);
    }

    // If still too long, do binary search for optimal count (fallback)
    if (currentTokens > MAX_INPUT_TOKENS) {
      let left = 0;
      let right = selectedMessages.length;
      let bestCount = 0;

      while (left <= right) {
        const mid = Math.floor((left + right) / 2);
        const testMessages = selectedMessages.slice(-mid); // Take most recent messages
        const testTokens = estimateTokens(testMessages, participants);

        if (testTokens <= MAX_INPUT_TOKENS) {
          bestCount = mid;
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }

      selectedMessages = selectedMessages.slice(-bestCount);
      currentTokens = estimateTokens(selectedMessages, participants);
    }

    console.log(`Filtered to ${selectedMessages.length} messages (from ${messages.length}) using api.ts-compatible logic. Final tokens: ${currentTokens}`);
    return selectedMessages;
  };

  // Function to determine participants from filtered messages and limit to top 8
  // Maintains consistent ID assignment based on original participant order
  const determineParticipantsFromFilteredMessages = (filteredMessages: any[]) => {
    // Count messages per participant in filtered messages
    const participantMessageCounts: Record<string, number> = {};

    filteredMessages.forEach(msg => {
      if (msg.sender && msg.sender !== "You" && msg.sender !== "\u200EYou") {
        participantMessageCounts[msg.sender] = (participantMessageCounts[msg.sender] || 0) + 1;
      }
    });

    // Sort participants by message count and take top 8
    const sortedParticipantNames = Object.entries(participantMessageCounts)
      .sort(([, countA], [, countB]) => countB - countA)
      .slice(0, 8)
      .map(([name]) => name);

    // Map back to original participant IDs to maintain consistency
    const finalParticipants = sortedParticipantNames.map(name => {
      const originalParticipant = initialParticipantsListWithIds.find(p => p.name === name);
      return originalParticipant || { id: `p${sortedParticipantNames.indexOf(name) + 1}`, name };
    });

    console.log(`Determined ${finalParticipants.length} participants from filtered messages: ${finalParticipants.map(p => `${p.name} (${p.id})`).join(', ')}`);
    return finalParticipants;
  };

  // --- Filter Messages and Determine Final Participants ---
  // First, filter messages by tokens using api.ts-compatible logic
  const tokenFilteredUserMessages = filterMessagesByTokens(preprocessedData.userMessages, initialParticipantsListWithIds);
  const tokenFilteredAllMessagesExceptSystem = filterMessagesByTokens(allMessagesExceptSystem, initialParticipantsListWithIds);

  // Determine participants based on analysis type and manual selection
  let participantsListWithIds;

  // Love analysis: Support manual participant selection for 2-person analysis
  if (detectedAnalysisType === 'love') {
    // If exactly 2 participants total, no need for manual selection
    if (initialParticipantsListWithIds.length === 2) {
      participantsListWithIds = initialParticipantsListWithIds;
      console.log(`Love analysis: Automatically using both participants: ${participantsListWithIds.map(p => p.name).join(', ')}`);
    }
    // If manual selection provided and more than 2 participants available
    else if (selectedParticipants && selectedParticipants.length > 0) {
      // Validate exactly 2 participants selected
      if (selectedParticipants.length !== 2) {
        throw new Error(`Love analysis requires exactly 2 participants, but ${selectedParticipants.length} were selected: ${selectedParticipants.join(', ')}`);
      }

      // Validate selected participants exist in chat
      const availableParticipantNames = initialParticipantsListWithIds.map(p => p.name);
      const invalidSelections = selectedParticipants.filter(name => !availableParticipantNames.includes(name));
      
      if (invalidSelections.length > 0) {
        throw new Error(`Selected participants not found in chat: ${invalidSelections.join(', ')}. Available participants: ${availableParticipantNames.join(', ')}`);
      }

      // Create participants list with selected participants
      participantsListWithIds = selectedParticipants.map(name => {
        const originalParticipant = initialParticipantsListWithIds.find(p => p.name === name);
        return originalParticipant!; // We validated existence above
      });

      console.log(`Love analysis: Using manually selected participants: ${participantsListWithIds.map(p => p.name).join(', ')}`);
    }
    // More than 2 participants available but no manual selection - use automatic selection
    else {
      participantsListWithIds = determineParticipantsFromFilteredMessages(tokenFilteredUserMessages).slice(0, 2);
      console.log(`Love analysis: No manual selection provided, automatically selected top 2 active participants: ${participantsListWithIds.map(p => p.name).join(', ')}`);
    }
  }
  // Non-love analysis: Always use automatic selection (existing behavior)
  else {
    participantsListWithIds = determineParticipantsFromFilteredMessages(tokenFilteredUserMessages);
    console.log(`${detectedAnalysisType} analysis: Using automatic participant selection`);
  }

  console.log(`Final analysis will focus on ${participantsListWithIds.length} participants: ${participantsListWithIds.map(p => p.name).join(', ')}`);

  // --- Prepare and Run LLM Batch Tasks ---
  if (env.GEMINI_API_KEY) {
    console.log("GEMINI_API_KEY available, preparing LLM batch tasks...");
    const llmStartTime = Date.now();

    // Helper to check if a task should be run based on selections
    const shouldRunTask = (taskId: string, selections?: string[]) => {
      // If selections is undefined, run all tasks (backward compatibility or default).
      // If selections is an array, only run if taskId is included.
      // If selections is an empty array, run no tasks.
      if (selections === undefined) return true;
      if (Array.isArray(selections) && selections.length === 0) return false;
      return Array.isArray(selections) && selections.includes(taskId);
    };

    // Use the shouldRunTaskForAnalysisType function from the config
    const shouldRunTaskForType = (taskId: string) => {
      return shouldRunTaskForAnalysisType(taskId, analysisTypeConfig, selectedLlmTaskIds);
    };

    // PHASE 1 FIX: Check cache status and create cache BEFORE token filtering using original messages
    const cacheStatus = getCacheStatus();

    if (!cacheStatus.active) {
      const estimatedTokenCount = estimateTokens(preprocessedData.userMessages, initialParticipantsListWithIds);

      if (estimatedTokenCount >= 5000) {
        console.log(`Creating cache with ${preprocessedData.userMessages.length} ORIGINAL messages (${estimatedTokenCount} tokens using api.ts-compatible estimation)`);
        await manageChatCache(env.GEMINI_API_KEY, 'create', preprocessedData.userMessages, initialParticipantsListWithIds);
      } else {
        console.log(`Skipping cache creation: ${estimatedTokenCount} tokens < 5000 threshold`);
      }
    }

    // PHASE 4 FIX: Enhanced cache status validation and monitoring
    const finalCacheStatus = getCacheStatus();
    const useCache = finalCacheStatus.active;

    console.log(`\n=== CACHE STATUS VALIDATION ===`);
    console.log(`Cache Active: ${useCache}`);
    console.log(`Cache Name: ${finalCacheStatus.cacheName || 'N/A'}`);
    console.log(`Message Strategy: ${useCache ? 'Empty arrays (cache-based)' : 'Full message content'}`);
    console.log(`Expected Cost Impact: ${useCache ? '75% reduction' : 'Standard cost'}`);
    console.log(`===============================\n`);

    // PHASE 4 FIX: Enhanced getMessagesForTask with validation and warnings
    const getMessagesForTask = (taskType: 'user' | 'all' | 'full') => {
      const cacheStatus = getCacheStatus();
      
      if (cacheStatus.active) {
        console.log(`🚀 Cache ACTIVE (${cacheStatus.cacheName}) - returning empty messages array for ${taskType} task`);
        return []; // Return empty array when cache is active
      }

      // Return appropriate message set when no cache
      let messages;
      switch (taskType) {
        case 'user':
          messages = tokenFilteredUserMessages;
          break;
        case 'all':
          messages = tokenFilteredAllMessagesExceptSystem;
          break;
        case 'full':
          messages = preprocessedData.userMessages;
          break;
        default:
          messages = tokenFilteredUserMessages;
      }

      console.log(`📨 No cache available - including ${messages.length} ${taskType} messages in task request`);
      console.log(`⚠️  WARNING: This will multiply input token costs by number of tasks!`);
      return messages;
    };

    // --- Common Tasks for Both Types ---
    // Person Stats (combined for all participants)
    console.log("Creating combined personStats task for all participants...");

    // Create a single task for all participants
    if (shouldRunTask('personStats', selectedLlmTaskIds)) {
      batchTasks.push({
        taskId: 'personStats', // Single task ID for all participants
        request: {
          task: 'generate_all_person_stats', // New task name for combined processing
          messages: getMessagesForTask('all'), // Use helper function
          participants: participantsListWithIds, // Provide final participants
          //participantStats: participantMetricsResult.participants // Pass participants array
        }
      });
    }

    // Add rankings task (common for both types)
    if (shouldRunTask('rankings', selectedLlmTaskIds)) {
      batchTasks.push({
        taskId: 'generate_rankings',
        request: { task: 'generate_rankings', messages: getMessagesForTask('all'), participants: participantsListWithIds, participantStats: participantMetricsResult.participants }
      });
    }

    // Calculate pairwise switches for frenemy analysis using token-filtered messages
    const pairwiseData = calculatePairwiseSwitches(tokenFilteredUserMessages);

    // Add social analysis tasks
    // DEACTIVATED: analyze_wall_of_shame - not used by frontend
    // DEACTIVATED: detect_flirtation - not used by frontend
    // DEACTIVATED: scan_for_gossip - not used by frontend
    // if (shouldRunTask('analyze_toxicity', selectedLlmTaskIds)) {
    //   batchTasks.push({ taskId: 'analyze_toxicity', request: { task: 'analyze_toxicity', messages: preprocessedData.userMessages, participants: participantsListWithIds } });
    // }

    // Add vibe metrics analysis task (language-independent replacement for vibe.ts)
    /*
    if (shouldRunTask('vibe_metrics', selectedLlmTaskIds)) {
      batchTasks.push({
        taskId: 'vibe_metrics',
        request: {
          task: 'analyze_vibe_metrics',
          messages: preprocessedData.userMessages,
          participants: participantsListWithIds,
          context: {
            messageCount: preprocessedData.userMessages.length,
            participantCount: participantsListWithIds.length
          }
        }
      });
    }
      */

    // Note: The frenemy dynamics task is added in the group-specific section below with taskId 'frenemyDynamics'

    // --- Type-Specific Tasks ---
    // Get the tasks for the current analysis type
    const currentTaskSet = analysisTypeConfig.tasks;

    console.log(`Using task set for analysis type: ${analysisTypeConfig.id} with ${currentTaskSet.length} tasks`);

    // Common task handling logic
    if (analysisTypeConfig.id === 'group' || analysisTypeConfig.id === 'love') {
      console.log(`Adding ${analysisTypeConfig.id}-specific LLM tasks...`);
      /*
      // Add dashboard chat stats for both chat and love analysis
      if (shouldRunTaskForType('dashboardChatStats')) {
        batchTasks.push({ taskId: 'dashboardChatStats', request: { task: 'generate_dashboard_chat_stats', messages: preprocessedData.userMessages, participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      }
    */
      // Add love-specific tasks if this is a love analysis
      if (analysisTypeConfig.id === 'love') {
        console.log("Adding love-specific LLM tasks...");

        // Add love compatibility task
        if (shouldRunTaskForType('calculate_love_compatibility')) {
          console.log("Adding calculate_love_compatibility task...");
          batchTasks.push({
            taskId: 'calculate_love_compatibility',
            request: {
              task: 'calculate_love_compatibility',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add love attachment styles task
        if (shouldRunTaskForType('analyze_love_attachment_styles')) {
          console.log("Adding analyze_love_attachment_styles task...");
          batchTasks.push({
            taskId: 'analyze_love_attachment_styles',
            request: {
              task: 'analyze_love_attachment_styles',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add new love analysis tasks

        // Add relationship summary task
        if (shouldRunTaskForType('analyze_relationship_summary')) {
          console.log("Adding analyze_relationship_summary task...");
          batchTasks.push({
            taskId: 'analyze_relationship_summary',
            request: {
              task: 'analyze_relationship_summary',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add empathy analysis task
        if (shouldRunTaskForType('analyze_empathy')) {
          console.log("Adding analyze_empathy task...");
          batchTasks.push({
            taskId: 'analyze_empathy',
            request: {
              task: 'analyze_empathy',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add closure catalyst task
        if (shouldRunTaskForType('analyze_closure_catalyst')) {
          console.log("Adding analyze_closure_catalyst task...");
          batchTasks.push({
            taskId: 'analyze_closure_catalyst',
            request: {
              task: 'analyze_closure_catalyst',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add conversation initiation task
        if (shouldRunTaskForType('analyze_conversation_initiation')) {
          console.log("Adding analyze_conversation_initiation task...");
          batchTasks.push({
            taskId: 'analyze_conversation_initiation',
            request: {
              task: 'analyze_conversation_initiation',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add reciprocity balance task
        if (shouldRunTaskForType('analyze_reciprocity_balance')) {
          console.log("Adding analyze_reciprocity_balance task...");
          batchTasks.push({
            taskId: 'analyze_reciprocity_balance',
            request: {
              task: 'analyze_reciprocity_balance',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add relationship health task
        if (shouldRunTaskForType('analyze_relationship_health')) {
          console.log("Adding analyze_relationship_health task...");
          batchTasks.push({
            taskId: 'analyze_relationship_health',
            request: {
              task: 'analyze_relationship_health',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add self love task
        if (shouldRunTaskForType('analyze_self_love')) {
          console.log("Adding analyze_self_love task...");
          batchTasks.push({
            taskId: 'analyze_self_love',
            request: {
              task: 'analyze_self_love',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add topic resonance task
        if (shouldRunTaskForType('analyze_topic_resonance')) {
          console.log("Adding analyze_topic_resonance task...");
          batchTasks.push({
            taskId: 'analyze_topic_resonance',
            request: {
              task: 'analyze_topic_resonance',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add timing patterns task
        if (shouldRunTaskForType('analyze_timing_patterns')) {
          console.log("Adding analyze_timing_patterns task...");
          batchTasks.push({
            taskId: 'analyze_timing_patterns',
            request: {
              task: 'analyze_timing_patterns',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add compatibility metrics task
        if (shouldRunTaskForType('analyze_compatibility_metrics')) {
          console.log("Adding analyze_compatibility_metrics task...");
          batchTasks.push({
            taskId: 'analyze_compatibility_metrics',
            request: {
              task: 'analyze_compatibility_metrics',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add jealousy indicators task
        if (shouldRunTaskForType('analyze_jealousy_indicators')) {
          console.log("Adding analyze_jealousy_indicators task...");
          batchTasks.push({
            taskId: 'analyze_jealousy_indicators',
            request: {
              task: 'analyze_jealousy_indicators',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add ghosting patterns task
        if (shouldRunTaskForType('analyze_ghosting_patterns')) {
          console.log("Adding analyze_ghosting_patterns task...");
          batchTasks.push({
            taskId: 'analyze_ghosting_patterns',
            request: {
              task: 'analyze_ghosting_patterns',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }

        // Add dashboard group slang task for love analysis
        /*
        if (shouldRunTaskForType('dashboardGroupSlang')) {
          console.log("Adding dashboardGroupSlang task for love analysis...");
          batchTasks.push({
            taskId: 'dashboardGroupSlang',
            request: {
              task: 'generate_dashboard_group_slang',
              messages: preprocessedData.userMessages,
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }
        */
        // Add dashboard character metrics task for love analysis
        if (shouldRunTaskForType('dashboardCharacterMetrics')) {
          console.log("Adding dashboardCharacterMetrics task for love analysis...");
          batchTasks.push({
            taskId: 'dashboardCharacterMetrics',
            request: {
              task: 'generate_dashboard_character_metrics',
              messages: getMessagesForTask('user'),
              participants: participantsListWithIds,
              participantStats: participantMetricsResult.participants
            }
          });
        }
      }

      // Add dashboard group slang for chat analysis (only for group analysis, not love)
      // if (analysisTypeConfig.id === 'group' && shouldRunTaskForType('dashboardGroupSlang')) {
      //   batchTasks.push({ taskId: 'dashboardGroupSlang', request: { task: 'generate_dashboard_group_slang', messages: getMessagesForTask('full'), participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      // }

      // Add dashboard character metrics for chat analysis (only for group analysis, not love)
      if (analysisTypeConfig.id === 'group' && shouldRunTaskForType('dashboardCharacterMetrics')) {
        batchTasks.push({ taskId: 'dashboardCharacterMetrics', request: { task: 'generate_dashboard_character_metrics', messages: getMessagesForTask('user'), participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      }

      // Add data metrics for chat analysis
      // if (shouldRunTaskForType('dataMetrics')) {
      //   batchTasks.push({ taskId: 'dataMetrics', request: { task: 'generate_data_metrics', messages: preprocessedData.userMessages, participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      // }

      // Add emotion metrics for chat analysis
      // if (shouldRunTaskForType('emotionMetrics')) {
      //   batchTasks.push({ taskId: 'emotionMetrics', request: { task: 'generate_emotion_metrics', messages: preprocessedData.userMessages, participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      // }

      // Add witty metrics for chat analysis
      // if (shouldRunTaskForType('wittyMetrics')) {
      //   batchTasks.push({ taskId: 'wittyMetrics', request: { task: 'generate_witty_metrics', messages: preprocessedData.userMessages, participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      // }

      // Add toxicity participants for chat analysis
      // if (shouldRunTaskForType('toxicityParticipants')) {
      //   batchTasks.push({ taskId: 'toxicityParticipants', request: { task: 'analyze_toxicity_participants', messages: preprocessedData.userMessages, participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      // }

      // Add frenemy dynamics for chat analysis
      // if (shouldRunTaskForType('frenemyDynamics')) {
      //   console.log("Adding frenemyDynamics task with comprehensive pairwise data...");
      //   batchTasks.push({
      //     taskId: 'frenemyDynamics',
      //     request: {
      //       task: 'analyze_frenemy_dynamics',
      //       messages: preprocessedData.userMessages,
      //       participants: participantsListWithIds,
      //       participantStats: participantMetricsResult.participants,
      //       context: {
      //         // Use the pairwise data calculated earlier for better analysis
      //         pairwiseData: pairwiseData,
      //         participantCount: participantsListWithIds.length,
      //         messageCount: preprocessedData.userMessages.length
      //       }
      //     }
      //   });
      // }

      // Add social graph data for chat analysis
      if (shouldRunTaskForType('socialGraphData')) {
        batchTasks.push({ taskId: 'socialGraphData', request: { task: 'generate_social_graph_data', messages: getMessagesForTask('user'), participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      }

      // Add respect scores for chat analysis
      // DEACTIVATED: respectScores - not used by frontend
      /*
      if (shouldRunTaskForType('respectScores')) {
        batchTasks.push({ taskId: 'rescpectScores', request: { task: 'generate_respect_scores', messages: getMessagesForTask('full'), participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      }
*/
      // Add dominant topics for chat analysis
      // DEACTIVATED: dominantTopics - not used by frontend
      /*
      if (shouldRunTaskForType('dominantTopics')) {
        batchTasks.push({ taskId: 'dominantTopics', request: { task: 'generate_dominant_topics', messages: getMessagesForTask('full'), participants: participantsListWithIds, participantStats: participantMetricsResult.participants } });
      }
*/
      // Add quiz questions for chat analysis
      // if (shouldRunTaskForType('quiz_questions')) {
      //   batchTasks.push({ taskId: 'quiz_questions', request: { task: 'generate_quiz_questions', messages: getMessagesForTask('full'), participants: participantsListWithIds } });
      // }

      // Add cringe moments for chat analysis
      if (shouldRunTaskForType('cringe_moments')) {
        batchTasks.push({ taskId: 'cringe_moments', request: { task: 'detect_cringe_moments', messages: getMessagesForTask('user'), participants: participantsListWithIds } });
      }

      // Add most likely to for chat analysis
      // DEACTIVATED: most_likely_to - not used by frontend /
      /*
      if (shouldRunTaskForType('most_likely_to')) {
        batchTasks.push({ taskId: 'most_likely_to', request: { task: 'generate_most_likely_to', messages: getMessagesForTask('user'), participants: participantsListWithIds } });
      }
*/
      // Add attachment styles for both chat and love analysis
      if (shouldRunTaskForType('analyze_attachment_styles')) {
        console.log("Adding analyze_attachment_styles task...");
        batchTasks.push({
          taskId: 'analyze_attachment_styles',
          request: {
            task: 'analyze_attachment_styles',
            messages: getMessagesForTask('user'),
            participants: participantsListWithIds,
            participantStats: participantMetricsResult.participants
          }
        });
      }

      // Add clout pulse data for chat analysis
      // if (shouldRunTaskForType('cloutPulseData')) {
      //   console.log("Adding cloutPulseData task...");
      //   batchTasks.push({
      //     taskId: 'cloutPulseData',
      //     request: {
      //       task: 'generate_clout_pulse_data',
      //       messages: preprocessedData.userMessages,
      //       participants: participantsListWithIds,
      //       participantStats: participantMetricsResult.participants
      //     }
      //   });
      // }

      // Add clique exclusion data for chat analysis
      // if (shouldRunTaskForType('cliqueExclusionData')) {
      //   console.log("Adding cliqueExclusionData task...");
      //   batchTasks.push({
      //     taskId: 'cliqueExclusionData',
      //     request: {
      //       task: 'generate_clique_exclusion_data',
      //       messages: preprocessedData.userMessages,
      //       participants: participantsListWithIds,
      //       participantStats: participantMetricsResult.participants
      //     }
      //   });
      // }

      // Add lover dynamics data for chat analysis
      // if (shouldRunTaskForType('loverDynamicsData')) {
      //   console.log("Adding loverDynamicsData task...");
      //   batchTasks.push({
      //     taskId: 'loverDynamicsData',
      //     request: {
      //       task: 'generate_lover_dynamics_data',
      //       messages: preprocessedData.userMessages,
      //       participants: participantsListWithIds,
      //       participantStats: participantMetricsResult.participants
      //     }
      //   });
      // }

      // Add compatibility metrics for group analysis
      if (shouldRunTaskForType('generate_compatibility_metrics')) {
        console.log("Adding compatibility_metrics task...");
        batchTasks.push({
          taskId: 'compatibility_metrics',
          request: {
            task: 'generate_compatibility_metrics',
            messages: getMessagesForTask('user'),
            participants: participantsListWithIds,
            participantStats: participantMetricsResult.participants
          }
        });
      }
    } else if (analysisTypeConfig.id === 'personal') {
      console.log("Adding personal-specific LLM tasks...");
      // DEACTIVATED: compatibility - not used by frontend
      // DEACTIVATED: textHistory - not used by frontend
    }

    // --- Execute Batch with NEW HYBRID CACHE MODEL ---
    try {
      console.log(`🚀 Running ${batchTasks.length} LLM tasks with HYBRID CACHE execution...`);
      console.log('🔧 NEW MODEL: CREATE CACHE ONCE + PARALLEL EXECUTION + SHARED CACHE');

      // 🚀 ENABLE NEW HYBRID CACHING: Cache creation + Parallel execution
      batchResults = await runLlmBatchTasks(env.GEMINI_API_KEY, batchTasks, true); // ENABLE hybrid caching
      console.log(`✅ LLM batch tasks completed in ${Date.now() - llmStartTime}ms with hybrid cache model`);

      // --- Process Batch Results and Token Usage ---
      console.log("Processing LLM batch results...");

      // Process all batch results
      for (const taskId in batchResults) {
        const result = batchResults[taskId];
        tokenUsage.addTaskUsage(taskId, result); // Update token usage

        if (!result.success) {
          console.error(`LLM task "${taskId}" failed:`, result.error);
          // Keep batchResults[taskId] as is, assembly logic will handle missing/failed results
        }
      }

      // Clean up the cache if it exists
      const cacheStatus = getCacheStatus();
      if (cacheStatus.active) {
        await manageChatCache(env.GEMINI_API_KEY, 'delete');
      }
    } catch (error) {
      console.error("Error executing LLM batch tasks:", error);
      // batchResults will remain empty or partially filled, assembly logic should handle this

      // Make sure to clean up the cache even if there was an error
      try {
        const cacheStatus = getCacheStatus();
        if (cacheStatus.active) {
          await manageChatCache(env.GEMINI_API_KEY, 'delete');
        }
      } catch (cacheError) {
        console.error("Error cleaning up cache:", cacheError);
      }
    }

  } else {
    console.warn("GEMINI_API_KEY not available, skipping ALL LLM tasks.");
    // batchResults will be empty
  }

  // --- Assemble Final Analysis Result ---
  console.log("Assembling final analysis result...");

  // --- Assemble Common Parts ---
  const analysisMetadata = {
    analysisId,
    timestamp: new Date().toISOString(),
    chatFileName: fileName,
    analysisType: analysisType,
    participantCount: participantCount,
    totalMessages: allMessagesExceptSystem.length
  };

  // Get personStats from the combined task
  let allPersonStats = getLlmResult<any[]>(batchResults, 'personStats', []);

  // Filter out any "Unknown" entries that might have been added by the sanitizer
  allPersonStats = allPersonStats.filter(stat =>
    stat && typeof stat === 'object' && stat.name && stat.name !== "Unknown"
  );

  // Run character classification to enhance personStats
  const characterClassificationResult = await classifyCharacters(preprocessedData.userMessages, {});

  // Enhance personStats with character classification results
  allPersonStats = allPersonStats.map(personStat => {
    const classification = characterClassificationResult.participantArchetypes[personStat.name];
    const personalityIndex = personalityIndexes[personStat.name];

    return {
      ...personStat,
      archetype: personStat.archetype || (classification ? classification.archetype : undefined),
      traits: personStat.traits || (classification ? classification.traits.join(", ") : undefined),
      personalityMetrics: personalityIndex || undefined
    };
  });

  // Validate personStats structure and content
  if (!allPersonStats || !Array.isArray(allPersonStats) || allPersonStats.length === 0) {
    console.error("personStats missing or empty from LLM result, creating fallback personStats");

    // Import the createFallbackPersonStats function from the task file
    const { createFallbackPersonStats } = await import('./llm/tasks/generateAllPersonStats.task');
    allPersonStats = createFallbackPersonStats(participantsListWithIds);
  } else {
    // Verify each personStat has required fields
    const validPersonStats = allPersonStats.filter(stat =>
      stat && typeof stat === 'object' && stat.name && stat.archetype
    );

    if (validPersonStats.length < allPersonStats.length) {
      console.warn(`Found ${allPersonStats.length - validPersonStats.length} invalid personStats, filtering them out`);
      allPersonStats = validPersonStats;
    }

    // Ensure we have a personStat for each participant
    const personStatNames = new Set(allPersonStats.map(p => p.name));

    // Check for missing participants and create fallbacks for them
    for (const participant of participantsListWithIds) {
      if (!personStatNames.has(participant.name)) {
        // Import the createFallbackPersonStats function from the task file
        const { createFallbackPersonStats } = await import('./llm/tasks/generateAllPersonStats.task');
        const fallbackStats = createFallbackPersonStats([participant]);
        if (fallbackStats.length > 0) {
          allPersonStats.push(fallbackStats[0]);
        }
      }
    }
  }

  analysisResult.personStats = allPersonStats;

  const charts = {
    sentimentOverTime: sentimentResult.sentimentOverTime,
    messageActivity: Object.entries(activityResult.activityByHour).map(([hour, count]) => ({ period: `${String(hour).padStart(2, '0')}:00`, count })),
    // participantComparisonMetrics can be added here if needed, using activity/sentiment results
  };

  // Call the appropriate result assembler based on analysis type
  if (analysisTypeConfig.resultAssembler && analysisTypeConfig.id !== 'personal') {
    // Use the configured result assembler
    console.log(`Using result assembler for ${analysisTypeConfig.name}`);
    return analysisTypeConfig.resultAssembler(
      analysisMetadata,
      preprocessedData,
      participantsListWithIds,
      activityResult,
      sentimentResult,
      analyzedMessages,
      participantMetricsResult,
      conversationStatsResult as any, // Type assertion to bypass type checking
      topicResult,
      mediaStatsResult,
      avgResponseTimeString,
      charts,
      batchResults,
      tokenUsage,
      allPersonStats // Pass the personStats
    );
  } else if (analysisTypeConfig.id === 'personal') {
    try {
      // Import the personal analysis assembler
      const { assemblePersonalAnalysisResult, createFallbackPersonalAnalysisResult } = await import('./resultAssemblers/personalAnalysisAssembler');

      try {
        // Try to use the regular assembler
        return await assemblePersonalAnalysisResult(
          analysisMetadata,
          preprocessedData,
          participantsListWithIds,
          activityResult,
          sentimentResult,
          analyzedMessages,
          participantMetricsResult,
          conversationStatsResult as any,
          topicResult,
          mediaStatsResult,
          avgResponseTimeString,
          charts,
          batchResults,
          tokenUsage,
          allPersonStats
        );
      } catch (assemblerError) {
        console.error("Error in assemblePersonalAnalysisResult:", assemblerError);
        // Use the fallback result creator if the assembler fails
        return createFallbackPersonalAnalysisResult(
          analysisMetadata,
          participantsListWithIds,
          activityResult,
          avgResponseTimeString,
          charts,
          allPersonStats
        );
      }
    } catch (importError) {
      console.error("Error importing personalAnalysisAssembler:", importError);
      // Return a minimal fallback result if even the import fails
      return {
        analysisMetadata,
        personStats: allPersonStats,
        error: "Failed to assemble personal analysis result"
      };
    }
  } else {
    // Default to group analysis for unknown types
    console.warn(`Unknown analysis type: ${analysisTypeConfig.id}, defaulting to group analysis`);
    // Get the default analysis type config
    const defaultConfig = getAnalysisTypeConfig('group');
    return defaultConfig.resultAssembler(
      analysisMetadata,
      preprocessedData,
      participantsListWithIds,
      activityResult,
      sentimentResult,
      analyzedMessages,
      participantMetricsResult,
      conversationStatsResult as any,
      topicResult,
      mediaStatsResult,
      avgResponseTimeString,
      charts,
      batchResults,
      tokenUsage,
      allPersonStats
    );
  }
}

// Note: We're using the result assemblers from the analysis type configuration
// In a full refactoring, all imports should be properly organized at the top
