<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chat Vibe Analyzer - File Upload</title>
    <style>
        :root {
            --primary-color: #6A0572;
            --secondary-color: #AB83A1;
            --accent-color: #4ECDC4;
            --background-color: #f9f7fd;
            --text-color: #333333;
            --border-color: #e0e0e0;
            --success-color: #4CAF50;
            --error-color: #f44336;
            --card-bg: #ffffff;
            --highlight-color: #FF6B6B;
            --neutral-color: #FFD166;
            --muted-text: #6c757d;
        }

        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: var(--text-color);
            background-color: var(--background-color);
            padding: 20px;
            background-image: linear-gradient(135deg, rgba(106, 5, 114, 0.05) 0%, rgba(78, 205, 196, 0.05) 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: var(--card-bg);
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
        }

        header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid rgba(106, 5, 114, 0.1);
        }

        h1 {
            color: var(--primary-color);
            margin-bottom: 10px;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
        }

        header p {
            color: var(--muted-text);
            font-size: 1.1rem;
            max-width: 600px;
            margin: 0 auto;
        }

        header p:last-of-type {
            margin-top: 15px;
            font-size: 0.95rem;
        }

        header a {
            color: var(--accent-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        header a:hover {
            color: var(--primary-color);
            text-decoration: underline;
        }

        .upload-container {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .upload-box {
            border: 2px dashed var(--border-color);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background-color: white;
            transition: all 0.3s ease;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
        }

        .upload-box:hover, .upload-box.dragover {
            border-color: var(--primary-color);
            background-color: rgba(106, 5, 114, 0.03);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .upload-box p {
            margin-bottom: 15px;
            color: var(--muted-text);
            font-size: 1.05rem;
        }

        .upload-box .icon {
            font-size: 56px;
            color: var(--primary-color);
            margin-bottom: 20px;
            display: inline-block;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
            100% { transform: translateY(0px); }
        }

        .file-input {
            display: none;
        }

        .btn {
            display: inline-block;
            background: linear-gradient(90deg, var(--primary-color), var(--accent-color));
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 50px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(106, 5, 114, 0.2);
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(106, 5, 114, 0.3);
        }

        .btn:active {
            transform: translateY(1px);
        }

        .btn:disabled {
            background: linear-gradient(90deg, #cccccc, #dddddd);
            cursor: not-allowed;
            box-shadow: none;
        }

        #upload-btn {
            margin: 0 auto;
            display: block;
            min-width: 200px;
        }

        .file-info {
            margin-top: 20px;
            padding: 20px;
            border-radius: 10px;
            background-color: white;
            border: 1px solid var(--border-color);
            display: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.03);
            transition: all 0.3s ease;
        }

        .file-info.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .file-info p {
            margin-bottom: 8px;
            color: var(--text-color);
        }

        .file-info p:last-child {
            margin-bottom: 0;
        }

        .file-info strong {
            color: var(--primary-color);
            font-weight: 600;
        }

        .analysis-options {
            margin-top: 20px;
            padding-top: 15px;
            border-top: 1px solid var(--border-color);
        }

        .analysis-type-buttons {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            width: 100%;
        }

        .analysis-type-btn {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid var(--border-color);
            background-color: white;
            color: var(--text-color);
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }

        .analysis-type-btn:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        }

        .analysis-type-btn.active {
            border-color: var(--primary-color);
            background-color: rgba(106, 5, 114, 0.05);
            box-shadow: 0 0 0 2px rgba(106, 5, 114, 0.1);
        }

        .btn-icon {
            font-size: 24px;
            margin-bottom: 8px;
        }

        .btn-text {
            font-weight: 600;
            color: var(--text-color);
        }

        .btn-default {
            font-size: 0.8rem;
            color: var(--muted-text);
            margin-top: 4px;
        }

        .analysis-description {
            font-size: 0.9rem;
            color: var(--muted-text);
            margin-top: 5px;
            font-style: italic;
        }

        .analysis-type-badge {
            display: inline-flex;
            align-items: center;
            background-color: rgba(106, 5, 114, 0.1);
            border-radius: 20px;
            padding: 6px 12px;
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--primary-color);
            font-weight: 600;
        }

        .analysis-type-icon {
            margin-right: 6px;
            font-size: 1.1rem;
        }

        /* Participant Selection Styles */
        .participant-selection {
            margin-top: 20px;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .participant-selection p {
            margin-bottom: 15px;
            color: var(--text-color);
            font-weight: 600;
        }

        .participant-loading {
            display: flex;
            align-items: center;
            gap: 10px;
            color: var(--muted-text);
            font-size: 0.9rem;
        }

        .mini-spinner {
            width: 16px;
            height: 16px;
            border: 2px solid rgba(106, 5, 114, 0.1);
            border-radius: 50%;
            border-top: 2px solid var(--primary-color);
            animation: spin 1s linear infinite;
        }

        .participant-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }

        .participant-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background-color: white;
            border: 2px solid var(--border-color);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
        }

        .participant-item:hover {
            border-color: var(--primary-color);
            background-color: rgba(106, 5, 114, 0.02);
        }

        .participant-item.selected {
            border-color: var(--primary-color);
            background-color: rgba(106, 5, 114, 0.05);
            box-shadow: 0 0 0 2px rgba(106, 5, 114, 0.1);
        }

        .participant-item.disabled {
            opacity: 0.5;
            cursor: not-allowed;
            background-color: #f5f5f5;
        }

        .participant-checkbox {
            margin-right: 12px;
            width: 18px;
            height: 18px;
            accent-color: var(--primary-color);
        }

        .participant-info {
            flex: 1;
        }

        .participant-name {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .participant-stats {
            font-size: 0.85rem;
            color: var(--muted-text);
        }

        .participant-error {
            color: var(--error-color);
            background-color: rgba(244, 67, 54, 0.1);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid var(--error-color);
            font-size: 0.9rem;
        }

        .participant-error p {
            margin: 0;
        }

        .selection-counter {
            margin-top: 15px;
            padding: 10px;
            background-color: rgba(106, 5, 114, 0.1);
            border-radius: 6px;
            font-size: 0.9rem;
            color: var(--primary-color);
            text-align: center;
            font-weight: 500;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .loading {
            display: none;
            text-align: center;
            margin: 30px 0;
            padding: 30px;
            border-radius: 10px;
            background-color: white;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .loading.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .spinner {
            border: 4px solid rgba(106, 5, 114, 0.1);
            border-radius: 50%;
            border-top: 4px solid var(--primary-color);
            border-right: 4px solid var(--accent-color);
            width: 50px;
            height: 50px;
            animation: spin 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
            margin: 0 auto 20px;
        }

        .loading p {
            color: var(--muted-text);
            font-size: 1.1rem;
            margin-top: 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .result-container {
            margin-top: 40px;
            display: none;
            border-top: 2px solid rgba(106, 5, 114, 0.1);
            padding-top: 30px;
        }

        .result-container.show {
            display: block;
            animation: fadeIn 0.8s ease;
        }

        .result-header {
            text-align: center;
            margin-bottom: 25px;
        }

        .result-header h2 {
            color: var(--primary-color);
            font-size: 1.8rem;
            margin-bottom: 10px;
        }

        .result-header p {
            color: var(--muted-text);
            max-width: 600px;
            margin: 0 auto;
        }

        .result-tabs {
            display: flex;
            border-bottom: 1px solid var(--border-color);
            margin-bottom: 15px;
        }

        .tab {
            padding: 12px 24px;
            cursor: pointer;
            border: 1px solid transparent;
            border-bottom: none;
            border-radius: 8px 8px 0 0;
            margin-right: 5px;
            background-color: #f5f5f5;
            font-weight: 500;
            color: var(--muted-text);
            transition: all 0.2s ease;
        }

        .tab:hover {
            background-color: #f0f0f0;
            color: var(--primary-color);
        }

        .tab.active {
            background-color: white;
            border-color: var(--border-color);
            border-bottom: 1px solid white;
            margin-bottom: -1px;
            color: var(--primary-color);
            font-weight: 600;
        }

        .tab-content {
            display: none;
            padding: 25px;
            background-color: white;
            border: 1px solid var(--border-color);
            border-radius: 0 0 8px 8px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .tab-content.active {
            display: block;
        }

        .json-viewer {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            overflow: auto;
            max-height: 600px;
            font-family: 'Courier New', Courier, monospace;
            white-space: pre-wrap;
            line-height: 1.5;
            font-size: 14px;
            color: #333;
            border: 1px solid #eaeaea;
        }

        .error-message {
            color: var(--error-color);
            background-color: rgba(244, 67, 54, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
            display: none;
            border-left: 4px solid var(--error-color);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .error-message.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .error-message p {
            margin: 0;
            font-weight: 500;
        }

        .success-message {
            color: var(--success-color);
            background-color: rgba(76, 175, 80, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-top: 25px;
            display: none;
            border-left: 4px solid var(--success-color);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }

        .success-message.show {
            display: block;
            animation: fadeIn 0.5s ease;
        }

        .success-message p {
            margin: 0;
            font-weight: 500;
        }

        /* Summary cards for key information */
        .summary-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin: 30px 0;
        }

        .summary-card {
            flex: 1 1 300px;
            background-color: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-top: 4px solid var(--accent-color);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            cursor: pointer;
        }

        .summary-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
        }

        .summary-card h3 {
            color: var(--primary-color);
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        .summary-card-content {
            color: var(--text-color);
        }

        .summary-card-stat {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 10px;
        }

        .summary-card-description {
            color: var(--muted-text);
            font-size: 0.9rem;
        }

        /* Data insights section */
        .data-insights-container {
            background-color: white;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            border-left: 4px solid var(--highlight-color);
        }

        .insights-title {
            color: var(--primary-color);
            margin-top: 0;
            margin-bottom: 20px;
            font-size: 1.4rem;
            border-bottom: 1px solid #eaeaea;
            padding-bottom: 10px;
        }

        .insights-list {
            list-style-type: none;
            padding: 0;
            margin: 0;
        }

        .insight-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px dashed #eaeaea;
        }

        .insight-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }

        .insight-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .no-insights {
            color: var(--muted-text);
            font-style: italic;
            text-align: center;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 5px;
        }

        /* Copy button styles */
        .copy-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 50px;
            padding: 8px 16px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            opacity: 0.8;
        }

        .copy-btn:hover {
            opacity: 1;
            transform: translateY(-2px);
        }

        /* Responsive styles */
        @media (max-width: 768px) {
            .container {
                padding: 15px;
                margin: 10px;
            }

            h1 {
                font-size: 2rem;
            }

            .upload-box {
                padding: 25px;
            }

            .btn {
                padding: 10px 20px;
                font-size: 15px;
            }

            .tab {
                padding: 10px 15px;
                font-size: 14px;
            }

            .json-viewer {
                padding: 15px;
                font-size: 13px;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 10px;
                margin: 5px;
            }

            h1 {
                font-size: 1.8rem;
            }

            header p {
                font-size: 0.9rem;
            }

            .upload-box {
                padding: 20px;
            }

            .upload-box .icon {
                font-size: 40px;
            }

            .btn {
                width: 100%;
            }

            .tab {
                padding: 8px 12px;
                font-size: 13px;
            }
        }
    </style>
    <!-- External CSS -->
    <link rel="stylesheet" href="css/upload-styles.css">
    <link rel="stylesheet" href="css/enhanced-json-viewer.css">
    <!-- Add JSON formatter library -->
    <script src="https://cdn.jsdelivr.net/npm/json-formatter-js@2.3.4/dist/json-formatter.umd.min.js"></script>
    <!-- JSON viewers -->
    <script src="js/json-viewer.js"></script>
    <script src="js/enhanced-json-viewer.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Chat Vibe Analyzer</h1>
            <p>Upload your WhatsApp (.txt) or Instagram (.json) chat export file for analysis</p>
            <p><a href="sample/sample-chat.txt" download>Download sample chat file</a> to test the analyzer</p>
        </header>

        <div class="upload-container">
            <div id="upload-box" class="upload-box">
                <div class="icon">📁</div>
                <p>Drag & drop your chat export file here</p>
                <p>or</p>
                <button id="select-file-btn" class="btn">Select File</button>
                <input type="file" id="file-input" class="file-input" accept=".txt,.json">
            </div>

            <div id="file-info" class="file-info">
                <p><strong>Selected file:</strong> <span id="file-name">No file selected</span></p>
                <p><strong>Size:</strong> <span id="file-size">0 KB</span></p>

                <div class="analysis-options">
                    <p><strong>Analysis Type:</strong></p>
                    <div class="analysis-type-buttons">
                        <button type="button" id="group-analysis-btn" class="analysis-type-btn active" data-type="group">
                            <span class="btn-icon">👥</span>
                            <span class="btn-text">Group Analysis</span>
                            <span class="btn-default">(Default)</span>
                        </button>
                        <button type="button" id="love-analysis-btn" class="analysis-type-btn" data-type="love">
                            <span class="btn-icon">❤️</span>
                            <span class="btn-text">Love Analysis</span>
                        </button>
                    </div>
                    <p class="analysis-description">
                        <span id="group-description">Group analysis examines group chat dynamics, relationships, and communication patterns.</span>
                        <span id="love-description" style="display: none;">Love analysis focuses on romantic and relationship dynamics between chat participants.</span>
                    </p>

                    <!-- Participant Selection for Love Analysis -->
                    <div id="participant-selection" class="participant-selection" style="display: none;">
                        <p><strong>Select 2 participants for relationship analysis:</strong></p>
                        <div id="participant-loading" class="participant-loading" style="display: none;">
                            <div class="mini-spinner"></div>
                            <span>Loading participants...</span>
                        </div>
                        <div id="participant-list" class="participant-list">
                            <!-- Participants will be populated here -->
                        </div>
                        <div id="participant-error" class="participant-error" style="display: none;">
                            <p>Failed to load participants. Please try selecting your file again.</p>
                        </div>
                    </div>
                </div>
            </div>

            <button id="upload-btn" class="btn" disabled>Analyze Chat</button>

            <div id="error-message" class="error-message">
                <p id="error-text"></p>
            </div>

            <div id="success-message" class="success-message">
                <p>Analysis completed successfully!</p>
            </div>

            <div id="loading" class="loading">
                <div class="spinner"></div>
                <p>Analyzing your chat file... This may take a minute.</p>
            </div>

            <div id="result-container" class="result-container">
                <div class="result-header">
                    <h2>Analysis Results</h2>
                    <p>Here's what we found in your chat file. Explore the details below.</p>
                    <div class="analysis-type-badge" id="analysis-type-badge">
                        <span id="analysis-type-icon">👥</span>
                        <span id="analysis-type-label">Group Analysis</span>
                    </div>
                </div>

                <div id="summary-cards" class="summary-cards">
                    <!-- Summary cards will be populated dynamically -->
                </div>

                <div class="result-tabs">
                    <div class="tab active" data-tab="enhanced">Enhanced View</div>
                    <div class="tab" data-tab="formatted">Standard View</div>
                    <div class="tab" data-tab="raw">Raw JSON</div>
                </div>
                <div id="enhanced-tab" class="tab-content active">
                    <div id="json-enhanced" class="json-viewer"></div>
                </div>
                <div id="formatted-tab" class="tab-content">
                    <div id="json-formatted" class="json-viewer"></div>
                </div>
                <div id="raw-tab" class="tab-content">
                    <pre id="json-raw" class="json-viewer"></pre>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Elements
            const uploadBox = document.getElementById('upload-box');
            const fileInput = document.getElementById('file-input');
            const selectFileBtn = document.getElementById('select-file-btn');
            const uploadBtn = document.getElementById('upload-btn');
            const fileInfo = document.getElementById('file-info');
            const fileName = document.getElementById('file-name');
            const fileSize = document.getElementById('file-size');
            const loading = document.getElementById('loading');
            const resultContainer = document.getElementById('result-container');
            const jsonFormatted = document.getElementById('json-formatted');
            const jsonRaw = document.getElementById('json-raw');
            const errorMessage = document.getElementById('error-message');
            const errorText = document.getElementById('error-text');
            const successMessage = document.getElementById('success-message');
            const tabs = document.querySelectorAll('.tab');
            const tabContents = document.querySelectorAll('.tab-content');
            const groupAnalysisBtn = document.getElementById('group-analysis-btn');
            const loveAnalysisBtn = document.getElementById('love-analysis-btn');
            const groupDescription = document.getElementById('group-description');
            const loveDescription = document.getElementById('love-description');
            let selectedAnalysisType = 'group'; // Default analysis type

            // Participant selection elements
            const participantSelection = document.getElementById('participant-selection');
            const participantLoading = document.getElementById('participant-loading');
            const participantList = document.getElementById('participant-list');
            const participantError = document.getElementById('participant-error');

            let selectedFile = null;
            let availableParticipants = [];
            let selectedParticipants = [];

            // Event listeners
            selectFileBtn.addEventListener('click', () => {
                fileInput.click();
            });

            fileInput.addEventListener('change', handleFileSelect);

            uploadBox.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadBox.classList.add('dragover');
            });

            uploadBox.addEventListener('dragleave', () => {
                uploadBox.classList.remove('dragover');
            });

            uploadBox.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadBox.classList.remove('dragover');

                if (e.dataTransfer.files.length) {
                    handleFiles(e.dataTransfer.files);
                }
            });

            uploadBtn.addEventListener('click', uploadFile);

            // Analysis type selection
            groupAnalysisBtn.addEventListener('click', function() {
                // Update active state
                groupAnalysisBtn.classList.add('active');
                loveAnalysisBtn.classList.remove('active');

                // Update selected type
                selectedAnalysisType = 'group';

                // Update description visibility
                groupDescription.style.display = 'block';
                loveDescription.style.display = 'none';

                // Hide participant selection
                participantSelection.style.display = 'none';
                selectedParticipants = [];
            });

            loveAnalysisBtn.addEventListener('click', function() {
                // Update active state
                loveAnalysisBtn.classList.add('active');
                groupAnalysisBtn.classList.remove('active');

                // Update selected type
                selectedAnalysisType = 'love';

                // Update description visibility
                groupDescription.style.display = 'none';
                loveDescription.style.display = 'block';

                // Show participant selection and load participants if file is selected
                if (selectedFile) {
                    loadParticipants();
                }
            });

            // Tab switching
            tabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    const tabId = tab.getAttribute('data-tab');

                    // Remove active class from all tabs and contents
                    tabs.forEach(t => t.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // Add active class to clicked tab and corresponding content
                    tab.classList.add('active');
                    document.getElementById(`${tabId}-tab`).classList.add('active');
                });
            });

            // Functions
            function handleFileSelect(e) {
                handleFiles(e.target.files);
            }

            function handleFiles(files) {
                if (files.length === 0) return;

                const file = files[0];

                // Check if file is a text or JSON file
                if (file.type !== 'text/plain' && file.type !== 'application/json' && 
                    !file.name.endsWith('.txt') && !file.name.endsWith('.json')) {
                    showError('Please select a valid text file (.txt) or JSON file (.json)');
                    return;
                }

                selectedFile = file;
                fileName.textContent = file.name;
                fileSize.textContent = formatFileSize(file.size);
                fileInfo.classList.add('show');
                uploadBtn.disabled = false;
                hideError();

                // If love analysis is selected, load participants
                if (selectedAnalysisType === 'love') {
                    loadParticipants();
                }
            }

            function formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';

                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));

                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            }

            // Participant loading and selection functions
            function loadParticipants() {
                if (!selectedFile) return;

                // Show loading state
                participantSelection.style.display = 'block';
                participantLoading.style.display = 'flex';
                participantList.style.display = 'none';
                participantError.style.display = 'none';
                selectedParticipants = [];

                // Create FormData to send file to participants endpoint
                const formData = new FormData();
                formData.append('file', selectedFile);

                fetch('/participants', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`Server error: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    availableParticipants = data.participants || [];
                    displayParticipants();
                })
                .catch(error => {
                    console.error('Error loading participants:', error);
                    showParticipantError();
                });
            }

            function displayParticipants() {
                participantLoading.style.display = 'none';
                participantError.style.display = 'none';

                // If exactly 2 participants, hide selection UI and automatically select both
                if (availableParticipants.length === 2) {
                    participantSelection.style.display = 'none';
                    selectedParticipants = availableParticipants.map(p => p.name);
                    console.log('Automatically selected both participants for love analysis:', selectedParticipants);
                    return;
                }

                // If less than 2 participants, show error
                if (availableParticipants.length < 2) {
                    participantList.innerHTML = '<p style="text-align: center; color: var(--error-color); padding: 20px;">This chat needs at least 2 participants for love analysis.</p>';
                    participantList.style.display = 'block';
                    return;
                }

                // Show participant selection for 3+ participants
                participantList.innerHTML = '';
                participantList.style.display = 'block';

                availableParticipants.forEach(participant => {
                    const participantItem = document.createElement('div');
                    participantItem.className = 'participant-item';
                    participantItem.innerHTML = `
                        <input type="checkbox" class="participant-checkbox" data-name="${participant.name}">
                        <div class="participant-info">
                            <div class="participant-name">${participant.name}</div>
                            <div class="participant-stats">${participant.messageCount} messages</div>
                        </div>
                    `;

                    const checkbox = participantItem.querySelector('.participant-checkbox');
                    checkbox.addEventListener('change', handleParticipantToggle);
                    participantItem.addEventListener('click', (e) => {
                        if (e.target !== checkbox) {
                            checkbox.click();
                        }
                    });

                    participantList.appendChild(participantItem);
                });

                // Add selection counter
                const counterDiv = document.createElement('div');
                counterDiv.className = 'selection-counter';
                counterDiv.id = 'selection-counter';
                counterDiv.textContent = 'Select exactly 2 participants';
                participantList.appendChild(counterDiv);
            }

            function handleParticipantToggle(e) {
                const checkbox = e.target;
                const participantName = checkbox.dataset.name;
                const participantItem = checkbox.closest('.participant-item');

                if (checkbox.checked) {
                    // Check if we can add more participants
                    if (selectedParticipants.length >= 2) {
                        checkbox.checked = false;
                        return;
                    }
                    selectedParticipants.push(participantName);
                    participantItem.classList.add('selected');
                } else {
                    selectedParticipants = selectedParticipants.filter(name => name !== participantName);
                    participantItem.classList.remove('selected');
                }

                // Update all checkboxes and items based on selection count
                const allCheckboxes = participantList.querySelectorAll('.participant-checkbox');
                const allItems = participantList.querySelectorAll('.participant-item');

                allItems.forEach((item, index) => {
                    const cb = allCheckboxes[index];
                    if (selectedParticipants.length >= 2 && !cb.checked) {
                        item.classList.add('disabled');
                        cb.disabled = true;
                    } else {
                        item.classList.remove('disabled');
                        cb.disabled = false;
                    }
                });

                // Update counter
                const counter = document.getElementById('selection-counter');
                if (counter) {
                    if (selectedParticipants.length === 0) {
                        counter.textContent = 'Select exactly 2 participants';
                        counter.style.backgroundColor = 'rgba(106, 5, 114, 0.1)';
                        counter.style.color = 'var(--primary-color)';
                    } else if (selectedParticipants.length === 1) {
                        counter.textContent = `Selected: ${selectedParticipants[0]} - Select 1 more`;
                        counter.style.backgroundColor = 'rgba(255, 193, 7, 0.1)';
                        counter.style.color = '#856404';
                    } else if (selectedParticipants.length === 2) {
                        counter.textContent = `Selected: ${selectedParticipants.join(' & ')} - Ready for analysis!`;
                        counter.style.backgroundColor = 'rgba(40, 167, 69, 0.1)';
                        counter.style.color = '#155724';
                    }
                }

                console.log('Selected participants:', selectedParticipants);
            }

            function showParticipantError() {
                participantLoading.style.display = 'none';
                participantList.style.display = 'none';
                participantError.style.display = 'block';
            }

            function uploadFile() {
                if (!selectedFile) {
                    showError('Please select a file first');
                    return;
                }

                // Validate participant selection for love analysis
                if (selectedAnalysisType === 'love') {
                    if (availableParticipants.length > 2 && selectedParticipants.length !== 2) {
                        showError('Please select exactly 2 participants for love analysis');
                        return;
                    }
                    if (availableParticipants.length < 2) {
                        showError('This chat needs at least 2 participants for love analysis');
                        return;
                    }
                }

                // Show loading spinner
                loading.classList.add('show');
                uploadBtn.disabled = true;
                hideError();
                successMessage.classList.remove('show');
                resultContainer.classList.remove('show');

                // Create FormData
                const formData = new FormData();
                formData.append('chatfile', selectedFile);

                // Add analysis type
                formData.append('analysisType', selectedAnalysisType);
                console.log(`Using analysis type: ${selectedAnalysisType}`);

                // Add selected participants for love analysis
                if (selectedAnalysisType === 'love' && selectedParticipants.length > 0) {
                    formData.append('selectedParticipants', JSON.stringify(selectedParticipants));
                    console.log(`Selected participants: ${selectedParticipants.join(', ')}`);
                }

                // Send request
                fetch('/submit', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    if (!response.ok) {
                        return response.text().then(text => {
                            throw new Error(`Server error: ${response.status} ${response.statusText}\n${text}`);
                        });
                    }
                    return response.json();
                })
                .then(data => {
                    // Hide loading spinner
                    loading.classList.remove('show');

                    // Show success message
                    successMessage.classList.add('show');

                    // Display the result
                    displayResult(data);

                    // Re-enable upload button
                    uploadBtn.disabled = false;
                })
                .catch(error => {
                    // Hide loading spinner
                    loading.classList.remove('show');

                    // Show error message
                    showError(`Error: ${error.message}`);

                    // Re-enable upload button
                    uploadBtn.disabled = false;
                });
            }

            function displayResult(data) {
                // Display raw JSON
                jsonRaw.textContent = JSON.stringify(data, null, 2);

                // Update analysis type badge
                const analysisTypeIcon = document.getElementById('analysis-type-icon');
                const analysisTypeLabel = document.getElementById('analysis-type-label');

                if (selectedAnalysisType === 'love') {
                    analysisTypeIcon.textContent = '❤️';
                    analysisTypeLabel.textContent = 'Love Analysis';
                } else {
                    analysisTypeIcon.textContent = '👥';
                    analysisTypeLabel.textContent = 'Group Analysis';
                }

                // Add copy button to raw JSON tab
                const rawTab = document.getElementById('raw-tab');
                if (!rawTab.querySelector('.copy-btn')) {
                    const copyBtn = document.createElement('button');
                    copyBtn.className = 'copy-btn';
                    copyBtn.textContent = 'Copy JSON';
                    copyBtn.addEventListener('click', () => {
                        navigator.clipboard.writeText(JSON.stringify(data, null, 2))
                            .then(() => {
                                copyBtn.textContent = 'Copied!';
                                setTimeout(() => {
                                    copyBtn.textContent = 'Copy JSON';
                                }, 2000);
                            })
                            .catch(err => {
                                console.error('Failed to copy: ', err);
                            });
                    });
                    rawTab.appendChild(copyBtn);
                }

                // Display enhanced JSON view
                try {
                    const jsonEnhanced = document.getElementById('json-enhanced');
                    createEnhancedJSONViewer(data, jsonEnhanced);
                } catch (error) {
                    console.error('Error creating enhanced JSON viewer:', error);
                    document.getElementById('json-enhanced').innerHTML =
                        '<p>Error creating enhanced view. Try the Standard View tab.</p>';
                }

                // Display standard formatted JSON as fallback
                try {
                    createJSONViewer(data, jsonFormatted);
                } catch (error) {
                    console.error('Error formatting JSON:', error);
                    jsonFormatted.innerHTML = '<p>Error formatting JSON. See raw tab for data.</p>';
                }

                // Create summary cards
                createSummaryCards(data);

                // Add data insights section
                addDataInsights(data);

                // Show result container
                resultContainer.classList.add('show');

                // Scroll to results
                resultContainer.scrollIntoView({ behavior: 'smooth' });
            }

            function addDataInsights(data) {
                // This function adds key insights from the data
                // We'll extract important information and display it in a more readable format

                // Only add insights if we have the right data structure
                if (!data || !data.dashboardChatStats) {
                    return;
                }

                // Create insights container if it doesn't exist
                let insightsContainer = document.getElementById('data-insights');
                if (!insightsContainer) {
                    insightsContainer = document.createElement('div');
                    insightsContainer.id = 'data-insights';
                    insightsContainer.className = 'data-insights-container';

                    // Add a title
                    const title = document.createElement('h3');
                    title.className = 'insights-title';
                    title.textContent = 'Key Insights';
                    insightsContainer.appendChild(title);

                    // Add the container after the summary cards
                    const summaryCards = document.getElementById('summary-cards');
                    summaryCards.parentNode.insertBefore(insightsContainer, summaryCards.nextSibling);
                } else {
                    // Clear existing insights
                    insightsContainer.innerHTML = '';
                    const title = document.createElement('h3');
                    title.className = 'insights-title';
                    title.textContent = 'Key Insights';
                    insightsContainer.appendChild(title);
                }

                // Add insights based on available data
                const insights = [];

                // Chat activity insights
                if (data.dashboardChatStats) {
                    const stats = data.dashboardChatStats;

                    if (stats.totalMessages) {
                        insights.push({
                            icon: '📊',
                            text: `This chat contains <strong>${stats.totalMessages}</strong> messages in total.`
                        });
                    }

                    if (stats.peakActivityTimeOfDay) {
                        insights.push({
                            icon: '⏰',
                            text: `Most messages are sent around <strong>${stats.peakActivityTimeOfDay}</strong>.`
                        });
                    }

                    if (stats.peakActivityDayOfWeek) {
                        insights.push({
                            icon: '📅',
                            text: `<strong>${stats.peakActivityDayOfWeek}</strong> is the most active day for this chat.`
                        });
                    }

                    if (stats.mood) {
                        insights.push({
                            icon: '😊',
                            text: `The overall mood of this chat is <strong>${stats.mood}</strong>.`
                        });
                    }
                }

                // Participant insights
                if (data.personStats && data.personStats.length > 0) {
                    // Get all participants (no deduplication)
                    const allParticipants = deduplicateParticipants(data.personStats);

                    // Add insight about total number of participants
                    insights.push({
                        icon: '👥',
                        text: `This chat has <strong>${allParticipants.length}</strong> participants.`
                    });

                    // Find most active participant
                    let mostActiveParticipant = allParticipants.length > 0 ? allParticipants[0] : null;
                    let highestMessageCount = 0;

                    allParticipants.forEach(person => {
                        if (person.rawStats && person.rawStats.messageCount > highestMessageCount) {
                            mostActiveParticipant = person;
                            highestMessageCount = person.rawStats.messageCount;
                        }
                    });

                    if (mostActiveParticipant && mostActiveParticipant.name && highestMessageCount > 0) {
                        insights.push({
                            icon: '👑',
                            text: `<strong>${mostActiveParticipant.name}</strong> is the most active participant with ${highestMessageCount} messages.`
                        });
                    }
                }

                // Add insights to the container
                if (insights.length > 0) {
                    const list = document.createElement('ul');
                    list.className = 'insights-list';

                    insights.forEach(insight => {
                        const item = document.createElement('li');
                        item.className = 'insight-item';
                        item.innerHTML = `<span class="insight-icon">${insight.icon}</span> ${insight.text}`;
                        list.appendChild(item);
                    });

                    insightsContainer.appendChild(list);
                } else {
                    // No insights available
                    const noInsights = document.createElement('p');
                    noInsights.className = 'no-insights';
                    noInsights.textContent = 'No detailed insights available for this chat.';
                    insightsContainer.appendChild(noInsights);
                }
            }

            function createSummaryCards(data) {
                const summaryCardsContainer = document.getElementById('summary-cards');
                summaryCardsContainer.innerHTML = ''; // Clear existing cards

                // Create cards based on available data
                if (data.dashboardChatStats) {
                    // Total Messages Card
                    addSummaryCard(
                        summaryCardsContainer,
                        'Total Messages',
                        data.dashboardChatStats.totalMessages || 0,
                        'Number of messages exchanged in this chat'
                    );

                    // Mood Card
                    if (data.dashboardChatStats.mood) {
                        addSummaryCard(
                            summaryCardsContainer,
                            'Chat Mood',
                            data.dashboardChatStats.mood,
                            data.dashboardChatStats.vibeDescription || 'Overall mood of the conversation'
                        );
                    }

                    // Most Used Emoji
                    if (data.dashboardChatStats.mostUsedEmoji) {
                        addSummaryCard(
                            summaryCardsContainer,
                            'Most Used Emoji',
                            data.dashboardChatStats.mostUsedEmoji,
                            `Used ${data.dashboardChatStats.emojiCount || 'multiple'} times in the conversation`
                        );
                    }
                }

                // Add participant info if available - no longer deduplicating
                if (data.personStats && data.personStats.length > 0) {
                    // Get all participants (no deduplication)
                    const allParticipants = deduplicateParticipants(data.personStats);

                    // Add card showing total participant count
                    addSummaryCard(
                        summaryCardsContainer,
                        'Participants',
                        allParticipants.length,
                        `This chat has ${allParticipants.length} unique participants`
                    );

                    // Find most active participant
                    let mostActiveParticipant = allParticipants.length > 0 ? allParticipants[0] : null;
                    let highestMessageCount = 0;

                    allParticipants.forEach(person => {
                        if (person.rawStats && person.rawStats.messageCount > highestMessageCount) {
                            mostActiveParticipant = person;
                            highestMessageCount = person.rawStats.messageCount;
                        }
                    });

                    if (mostActiveParticipant) {
                        addSummaryCard(
                            summaryCardsContainer,
                            'Top Participant',
                            mostActiveParticipant.name,
                            `${mostActiveParticipant.mood || 'Active'} participant with ${highestMessageCount} messages`
                        );
                    }
                }

                // Add compatibility if it's a personal chat
                if (data.personal && data.personal.compatibility) {
                    addSummaryCard(
                        summaryCardsContainer,
                        'Compatibility Score',
                        `${data.personal.compatibility}%`,
                        'How well these participants communicate with each other'
                    );
                }
            }

            // Helper function that no longer deduplicates participants
            function deduplicateParticipants(participants) {
                if (!Array.isArray(participants)) return [];

                // Simply return all valid participants without deduplication
                return participants.filter(participant =>
                    participant &&
                    typeof participant === 'object' &&
                    participant.name
                );
            }

            function addSummaryCard(container, title, stat, description, dataPath) {
                const card = document.createElement('div');
                card.className = 'summary-card';

                // Determine data path based on title if not provided
                if (!dataPath) {
                    switch (title) {
                        case 'Total Messages':
                            dataPath = 'dashboardChatStats.totalMessages';
                            break;
                        case 'Chat Mood':
                            dataPath = 'dashboardChatStats.mood';
                            break;
                        case 'Most Used Emoji':
                            dataPath = 'dashboardChatStats.mostUsedEmoji';
                            break;
                        case 'Top Participant':
                            dataPath = 'personStats';
                            break;
                        case 'Compatibility Score':
                            dataPath = 'personal.compatibility';
                            break;
                        default:
                            dataPath = '';
                    }
                }

                card.setAttribute('data-path', dataPath);

                card.innerHTML = `
                    <h3>${title}</h3>
                    <div class="summary-card-content">
                        <div class="summary-card-stat">${stat}</div>
                        <div class="summary-card-description">${description}</div>
                    </div>
                `;

                // Add click event to highlight the relevant data in the JSON viewer
                card.addEventListener('click', () => {
                    // Switch to enhanced view tab
                    document.querySelector('.tab[data-tab="enhanced"]').click();

                    // Find the path in the enhanced viewer and highlight it
                    highlightJsonPath(dataPath);
                });

                container.appendChild(card);
            }

            function highlightJsonPath(path) {
                if (!path) return;

                // Get the enhanced JSON viewer
                const jsonEnhanced = document.getElementById('json-enhanced');

                // If we're using the enhanced viewer, try to find and highlight the path
                const enhancedViewer = jsonEnhanced.querySelector('.enhanced-json-viewer');
                if (enhancedViewer) {
                    // If there's a search input, use it to search for the path
                    const searchInput = enhancedViewer.querySelector('.json-search-input');
                    if (searchInput) {
                        // Extract the last part of the path for searching
                        const pathParts = path.split('.');
                        const searchTerm = pathParts[pathParts.length - 1];

                        // Set the search term and trigger the input event
                        searchInput.value = searchTerm;
                        searchInput.dispatchEvent(new Event('input'));

                        // Scroll to the first match
                        setTimeout(() => {
                            const firstMatch = enhancedViewer.querySelector('mark');
                            if (firstMatch) {
                                firstMatch.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }, 300);
                    }

                    // Also try to click the appropriate category button if it matches the path
                    const categoryButtons = enhancedViewer.querySelectorAll('.json-category-btn');
                    categoryButtons.forEach(button => {
                        const category = button.dataset.category;
                        const categoryObj = window.jsonViewer?.categories?.find(c => c.id === category);
                        if (categoryObj && categoryObj.path === path) {
                            button.click();
                        }
                    });
                }
            }

            function showError(message) {
                errorText.textContent = message;
                errorMessage.classList.add('show');
            }

            function hideError() {
                errorMessage.classList.remove('show');
            }
        });
    </script>
</body>
</html>
