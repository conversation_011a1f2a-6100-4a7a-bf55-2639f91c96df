import * as fs from 'fs';
import * as path from 'path';

/**
 * Unicode Reverter Utility
 * Converts Unicode escape sequences back to actual Unicode characters
 * Handles Turkish, Arabic, emoji, and other non-English characters
 */

interface UnicodeReverterOptions {
  inputFile: string;
  outputFile?: string;
  preserveOriginal?: boolean;
  verbose?: boolean;
}

class UnicodeReverter {
  private options: UnicodeReverterOptions;

  constructor(options: UnicodeReverterOptions) {
    this.options = {
      preserveOriginal: true,
      verbose: false,
      ...options
    };
  }

  /**
   * Converts Unicode escape sequences to actual Unicode characters
   * Handles UTF-8 encoded sequences that are common in Instagram exports
   */
  private convertUnicodeEscapes(text: string): string {
    // First, convert all \uXXXX sequences to their byte values
    const withBytes = text.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
      const codePoint = parseInt(hex, 16);
      return String.fromCharCode(codePoint);
    });

    // Now decode UTF-8 sequences
    return this.decodeUtf8(withBytes);
  }

  /**
   * Decodes UTF-8 byte sequences to proper Unicode characters
   * This handles the case where Instagram exports UTF-8 bytes as Unicode escapes
   */
  private decodeUtf8(text: string): string {
    try {
      // Convert string to UTF-8 bytes
      const encoder = new TextEncoder();
      const decoder = new TextDecoder('utf-8');

      // Get the bytes from the string
      const bytes = [];
      for (let i = 0; i < text.length; i++) {
        bytes.push(text.charCodeAt(i));
      }

      // Create Uint8Array and decode as UTF-8
      const uint8Array = new Uint8Array(bytes);
      return decoder.decode(uint8Array);
    } catch (error) {
      // If UTF-8 decoding fails, return original text
      return text;
    }
  }

  /**
   * Processes a JSON object recursively to convert Unicode escapes
   */
  private processObject(obj: any): any {
    if (typeof obj === 'string') {
      return this.convertUnicodeEscapes(obj);
    } else if (Array.isArray(obj)) {
      return obj.map(item => this.processObject(item));
    } else if (obj !== null && typeof obj === 'object') {
      const result: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Convert both keys and values
        const convertedKey = this.convertUnicodeEscapes(key);
        result[convertedKey] = this.processObject(value);
      }
      return result;
    }
    return obj;
  }

  /**
   * Analyzes the input file to show Unicode escape statistics
   */
  private analyzeUnicodeEscapes(content: string): {
    totalEscapes: number;
    uniqueEscapes: Set<string>;
    examples: string[];
  } {
    const escapePattern = /\\u([0-9a-fA-F]{4})/g;
    const matches = content.match(escapePattern) || [];
    const uniqueEscapes = new Set(matches);
    
    // Get some examples with context
    const examples: string[] = [];
    const lines = content.split('\n');
    
    for (let i = 0; i < lines.length && examples.length < 10; i++) {
      if (escapePattern.test(lines[i])) {
        examples.push(`Line ${i + 1}: ${lines[i].trim()}`);
      }
    }

    return {
      totalEscapes: matches.length,
      uniqueEscapes,
      examples
    };
  }

  /**
   * Main method to process the file
   */
  async processFile(): Promise<void> {
    try {
      // Check if input file exists
      if (!fs.existsSync(this.options.inputFile)) {
        throw new Error(`Input file not found: ${this.options.inputFile}`);
      }

      if (this.options.verbose) {
        console.log(`Processing file: ${this.options.inputFile}`);
      }

      // Read the input file
      const inputContent = fs.readFileSync(this.options.inputFile, 'utf8');

      // Analyze Unicode escapes before processing
      const analysis = this.analyzeUnicodeEscapes(inputContent);
      
      if (this.options.verbose) {
        console.log('\n=== Unicode Escape Analysis ===');
        console.log(`Total Unicode escapes found: ${analysis.totalEscapes}`);
        console.log(`Unique escape sequences: ${analysis.uniqueEscapes.size}`);
        console.log('\nUnique escapes:');
        Array.from(analysis.uniqueEscapes).sort().forEach(escape => {
          const converted = this.convertUnicodeEscapes(escape);
          console.log(`  ${escape} → "${converted}"`);
        });
        
        if (analysis.examples.length > 0) {
          console.log('\nExamples in context:');
          analysis.examples.forEach(example => console.log(`  ${example}`));
        }
        console.log('================================\n');
      }

      // Parse JSON
      let jsonData: any;
      try {
        jsonData = JSON.parse(inputContent);
      } catch (error) {
        throw new Error(`Invalid JSON format: ${error}`);
      }

      // Process the JSON object
      const processedData = this.processObject(jsonData);

      // Determine output file
      const outputFile = this.options.outputFile || 
        this.generateOutputFileName(this.options.inputFile);

      // Write the processed data
      const outputContent = JSON.stringify(processedData, null, 2);
      fs.writeFileSync(outputFile, outputContent, 'utf8');

      if (this.options.verbose) {
        console.log(`✅ Successfully processed file!`);
        console.log(`📁 Output saved to: ${outputFile}`);
        console.log(`📊 Converted ${analysis.totalEscapes} Unicode escape sequences`);
      }

      // Show some before/after examples
      if (this.options.verbose && analysis.examples.length > 0) {
        console.log('\n=== Before/After Examples ===');
        const processedContent = JSON.stringify(processedData, null, 2);
        const processedLines = processedContent.split('\n');
        
        for (let i = 0; i < Math.min(3, analysis.examples.length); i++) {
          const originalExample = analysis.examples[i];
          const lineMatch = originalExample.match(/Line (\d+):/);
          if (lineMatch) {
            const lineNum = parseInt(lineMatch[1]) - 1;
            if (lineNum < processedLines.length) {
              console.log(`\nExample ${i + 1}:`);
              console.log(`Before: ${originalExample.split(': ')[1]}`);
              console.log(`After:  ${processedLines[lineNum].trim()}`);
            }
          }
        }
        console.log('==============================');
      }

    } catch (error) {
      console.error(`❌ Error processing file: ${error}`);
      throw error;
    }
  }

  /**
   * Generates output filename based on input filename
   */
  private generateOutputFileName(inputFile: string): string {
    const ext = path.extname(inputFile);
    const base = path.basename(inputFile, ext);
    const dir = path.dirname(inputFile);
    return path.join(dir, `${base}_unicode_converted${ext}`);
  }
}

// Export for use as module
export { UnicodeReverter, UnicodeReverterOptions };

// CLI usage when run directly
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
Unicode Reverter - Convert Unicode escape sequences to actual characters

Usage:
  tsx unicode-reverter.ts <input-file> [output-file]
  
Options:
  input-file   : Path to the JSON file with Unicode escapes
  output-file  : Optional output file path (default: adds '_unicode_converted' suffix)
  
Examples:
  tsx unicode-reverter.ts message_1_3.json
  tsx unicode-reverter.ts message_1_3.json converted.json
    `);
    process.exit(1);
  }

  const inputFile = args[0];
  const outputFile = args[1];

  const reverter = new UnicodeReverter({
    inputFile,
    outputFile,
    verbose: true
  });

  reverter.processFile()
    .then(() => {
      console.log('\n🎉 Unicode conversion completed successfully!');
    })
    .catch((error) => {
      console.error('\n💥 Unicode conversion failed:', error.message);
      process.exit(1);
    });
}
