import {
  GoogleGenAI,
  Content,
  GenerateContentResponse,
  CountTokensResponse,
  HarmCategory,
  HarmBlockThreshold,
} from "@google/genai";
import { LlmTaskRequest, LlmTaskResponse, LlmBatchTask, CacheStatus, TaskDefinition } from "./types";
// Removed: import { getPromptInstruction as getGlobalPromptInstruction } from "./prompts";
// Removed: import { sanitizeTaskResult as globalSanitizeTaskResult } from "./sanitizers";
import { ParsedMessage } from "../../parser";
import { calculatePairwiseSwitches, PairwiseSwitchData } from "../social/frenemy";
import { retry } from "../../utils/retry";
import { getTaskDefinition } from "./taskRegistry"; // dispatchTask and isTaskRegistered might not be needed here anymore
import { ActivityMetrics, analyzeActivity } from "../core/activity";
import { SentimentMetrics, analyzeSentiment } from "../core/sentiment";

let cachedTokenCount = 0;
let actualCacheCreationTokens = 0; // NEW: Track actual cache creation tokens separately
const INPUT_TOKEN_COST_PER_MILLION = 0.1;
const OUTPUT_TOKEN_COST_PER_MILLION = 0.4;
const CACHE_HIT_COST_PER_MILLION = 0.025; // Cache hit cost (1/4 of input cost)
const CACHE_STORAGE_COST_PER_MILLION_PER_HOUR = 1.0;
const MAX_REASONABLE_TOKENS = 100000; // Maximum tokens for cache creation
const MAX_INPUT_TOKENS = 100000; // Keep only last 100k tokens of chat
let activeCache: any | null = null;
const CACHE_TTL = '60s';

// Global model name constant - Never change this
const MODEL_NAME = "gemini-2.0-flash-001";
// const MODEL_NAME = "gemini-2.5-pro-preview-05-06";
// PHASE 2 FIX: Add cache duration tracking variables
let cacheCreatedAt: number | null = null;
let cacheTokenCount: number = 0;

// Add these constants at the top of the file
const RATE_LIMIT_WINDOW_MS = 60000; // 1 minute in milliseconds
const MAX_REQUESTS_PER_WINDOW = 40; // Adjust based on your quota
const RATE_LIMIT_QUEUE: {timestamp: number}[] = [];
const BACKOFF_INITIAL_MS = 1000;
const BACKOFF_MAX_MS = 30000;

// Add this function to implement rate limiting
async function enforceRateLimit(): Promise<void> {
  const now = Date.now();
  
  // Remove timestamps older than the window
  while (RATE_LIMIT_QUEUE.length > 0 && now - RATE_LIMIT_QUEUE[0].timestamp > RATE_LIMIT_WINDOW_MS) {
    RATE_LIMIT_QUEUE.shift();
  }
  
  // If we've hit the limit, wait until we can proceed
  if (RATE_LIMIT_QUEUE.length >= MAX_REQUESTS_PER_WINDOW) {
    const oldestTimestamp = RATE_LIMIT_QUEUE[0].timestamp;
    const timeToWait = oldestTimestamp + RATE_LIMIT_WINDOW_MS - now;
    console.log(`Rate limit reached. Waiting ${timeToWait}ms before next request.`);
    await new Promise(resolve => setTimeout(resolve, timeToWait + 100)); // Add 100ms buffer
    return enforceRateLimit(); // Recursively check again after waiting
  }
  
  // Add current timestamp to the queue
  RATE_LIMIT_QUEUE.push({ timestamp: now });
}

// Modify the retry function to use exponential backoff for 429 errors
export async function retryWithBackoff<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  initialDelayMs: number = BACKOFF_INITIAL_MS
): Promise<T> {
  let retries = 0;
  let delay = initialDelayMs;
  
  while (true) {
    try {
      // Enforce rate limit before making the request
      await enforceRateLimit();
      return await fn();
    } catch (error: any) {
      retries++;
      
      // If we've reached max retries, throw the error
      if (retries > maxRetries) {
        throw error;
      }
      
      // If it's a rate limit error (429), use exponential backoff
      if (error?.message?.includes('429') || error?.status === 429) {
        // Extract retry delay from error if available
        let retryDelay = delay;
        try {
          const match = error.message.match(/retryDelay:"(\d+)s"/);
          if (match && match[1]) {
            retryDelay = parseInt(match[1]) * 1000;
          }
        } catch (e) {
          // Ignore parsing errors
        }
        
        // Use exponential backoff with jitter
        delay = Math.min(retryDelay * Math.pow(2, retries - 1) * (0.8 + Math.random() * 0.4), BACKOFF_MAX_MS);
      } else {
        // For other errors, use linear backoff
        delay = initialDelayMs * retries;
      }
      
      console.log(`Retrying after ${delay}ms due to error: ${error.message}`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
}

async function createChatCache(
  genAI: GoogleGenAI,
  messages: ParsedMessage[],
  participants: { id: string; name: string }[],
  modelName: string
): Promise<{ cache: any | null; tokenCount: number }> {
  try {
    // PHASE 2 FIX: Track cache creation time
    cacheCreatedAt = Date.now();

    cachedTokenCount = 0;

    // Create initial message text
    const fullMessageText = messages.map((msg, index) =>
      `${index}: [${msg.timestamp?.toISOString() || 'No Timestamp'}] ${msg.sender}: ${msg.text}`
    ).join('\n');

    // Create base cache content structure
    const participantText = `Participants: ${participants?.map(p => `${p.name} (ID: ${p.id})`).join(', ') || 'Unknown'}`;
    const baseContent = `Chat Analysis Data:\n\n${participantText}\n\nMessages:\n`;

    // Calculate available space for messages within token limit
    const baseTokens = Math.ceil((baseContent.length + 200) / 5.5); // Base content + buffer
    const availableTokensForMessages = MAX_REASONABLE_TOKENS - baseTokens;
    const maxMessageChars = Math.floor(availableTokensForMessages * 5.5); // Convert back to characters

    // Truncate messages if they exceed the limit
    let messageTextForPrompt = fullMessageText;
    if (fullMessageText.length > maxMessageChars) {
      console.log(`⚠️ Messages exceed ${MAX_REASONABLE_TOKENS} token limit. Truncating from ${Math.ceil(fullMessageText.length / 5.5)} to ~${availableTokensForMessages} tokens...`);

      // Take the most recent messages that fit within the limit
      const messageLines = fullMessageText.split('\n');
      let truncatedLines: string[] = [];
      let currentLength = 0;

      // Add messages from the end (most recent) until we hit the limit
      for (let i = messageLines.length - 1; i >= 0; i--) {
        const lineLength = messageLines[i].length + 1; // +1 for newline
        if (currentLength + lineLength <= maxMessageChars) {
          truncatedLines.unshift(messageLines[i]);
          currentLength += lineLength;
        } else {
          break;
        }
      }

      messageTextForPrompt = truncatedLines.join('\n');
      const truncatedMessageCount = truncatedLines.length;
      const originalMessageCount = messageLines.length;
      console.log(`✓ Truncated to ${truncatedMessageCount}/${originalMessageCount} messages (${((truncatedMessageCount/originalMessageCount)*100).toFixed(1)}% of original)`);
    }

    // NEW SDK: Create cache content using the correct structure
    const cacheContent = `${baseContent}${messageTextForPrompt}`;

    const totalCharCount = cacheContent.length + 200;
    let estimatedTokens = Math.ceil(totalCharCount / 5.5);

    // Final safety check - should not exceed limit now
    if (estimatedTokens > MAX_REASONABLE_TOKENS) {
      console.warn(`⚠️ Cache content still exceeds limit after truncation: ${estimatedTokens} tokens. Capping at ${MAX_REASONABLE_TOKENS}.`);
      estimatedTokens = MAX_REASONABLE_TOKENS;
    }

    cachedTokenCount = estimatedTokens;
    actualCacheCreationTokens = estimatedTokens; // NEW: Store actual cache creation tokens
    cacheTokenCount = estimatedTokens; // Store for duration calculation

    console.log(`Creating cache with ${estimatedTokens} estimated tokens at ${new Date(cacheCreatedAt).toISOString()}...`);

    // NEW SDK: Use the updated cache creation API
    const cache = await genAI.caches.create({
      model: modelName,
      config: {
        displayName: 'chat-analysis-cache',
        systemInstruction: "You are an expert chat analysis assistant. You have access to chat messages and can analyze them to answer questions about participants, conversations, and group dynamics.",
        contents: [{
          role: 'user',
          parts: [{ text: cacheContent }]
        }],
        ttl: CACHE_TTL
      }
    });

    console.log(`✅ Cache created successfully: ${cache.name}`);
    return { cache, tokenCount: estimatedTokens };
  } catch (error) {
    cacheCreatedAt = null; // Reset on failure
    console.error("Error creating chat cache:", error);
    return { cache: null, tokenCount: 0 };
  }
}

async function deleteCache(genAI: GoogleGenAI): Promise<boolean> {
  try {
    if (activeCache && activeCache.name) { // Ensure activeCache and its name property exist
      // PHASE 2 FIX: Calculate actual cache duration
      const cacheDeletedAt = Date.now();
      let actualStorageCost = 0;
      
      if (cacheCreatedAt && cacheTokenCount > 0) {
        const actualDurationHours = (cacheDeletedAt - cacheCreatedAt) / (1000 * 60 * 60);
        actualStorageCost = (cacheTokenCount / 1000000) * CACHE_STORAGE_COST_PER_MILLION_PER_HOUR * actualDurationHours;
        
        console.log(`Cache storage duration: ${((cacheDeletedAt - cacheCreatedAt) / 1000).toFixed(2)} seconds`);
        console.log(`Cache storage cost: $${actualStorageCost.toFixed(6)} (${cacheTokenCount} tokens × ${actualDurationHours.toFixed(4)} hours)`);
      }
      
      await genAI.caches.delete({ name: activeCache.name });
      console.log(`Cache ${activeCache.name} deleted successfully.`);
      
      // Reset tracking variables
      activeCache = null;
      cachedTokenCount = 0;
      actualCacheCreationTokens = 0; // NEW: Reset actual cache creation tokens
      cacheCreatedAt = null;
      cacheTokenCount = 0;
      
      return true;
    }
    // If activeCache or activeCache.name is null/undefined, there's nothing to delete.
    return false;
  } catch (error: any) {
    // Check if the error is the specific "CachedContent not found" error
    if (error.message && (error.message.includes("CachedContent not found") || error.message.includes("permission denied"))) {
      console.warn(`Attempted to delete cache ${activeCache?.name}, but it was not found or permission was denied. Assuming already deleted or inaccessible.`);
      activeCache = null; // Still clear local reference
      cachedTokenCount = 0;
      actualCacheCreationTokens = 0; // NEW: Reset actual cache creation tokens
      // Always reset tracking variables
      cacheCreatedAt = null;
      cacheTokenCount = 0;
      return true; // Treat as "successfully handled" for the purpose of cleanup
    }
    // For other errors, log them as critical
    console.error(`Error deleting cache ${activeCache?.name}:`, error);
    
    // Always reset tracking variables
    cacheCreatedAt = null;
    cacheTokenCount = 0;
    actualCacheCreationTokens = 0; // NEW: Reset actual cache creation tokens
    
    return false;
  }
}

function calculateTokenCost(
  inputTokens: number, outputTokens: number, useCaching: boolean = false,
  cachedTokens: number = 0, cacheAccessCount: number = 1
): { inputCost: number, outputCost: number, cachingCost: number, storageCost: number, totalCost: number } {
  // Cache access cost remains the same
  const cacheAccessCost = useCaching && cachedTokens > 0 ? (cachedTokens / 1000000) * CACHE_HIT_COST_PER_MILLION * cacheAccessCount : 0;
  
  // PHASE 2 FIX: Calculate actual storage cost based on real duration
  let storageCost = 0;
  if (useCaching && cachedTokens > 0 && cacheCreatedAt) {
    const currentTime = Date.now();
    const actualDurationHours = (currentTime - cacheCreatedAt) / (1000 * 60 * 60);
    storageCost = (cachedTokens / 1000000) * CACHE_STORAGE_COST_PER_MILLION_PER_HOUR * actualDurationHours;
  }
  
  const inputCost = (inputTokens / 1000000) * INPUT_TOKEN_COST_PER_MILLION;
  const outputCost = (outputTokens / 1000000) * OUTPUT_TOKEN_COST_PER_MILLION;
  const cachingCost = cacheAccessCost;
  
  const roundedInputCost = Number(inputCost.toFixed(6));
  const roundedOutputCost = Number(outputCost.toFixed(6));
  const roundedCachingCost = Number(cachingCost.toFixed(6));
  const roundedStorageCost = Number(storageCost.toFixed(6));
  const totalCost = Number((roundedInputCost + roundedOutputCost + roundedCachingCost + roundedStorageCost).toFixed(6));
  
  return {
    inputCost: roundedInputCost,
    outputCost: roundedOutputCost,
    cachingCost: roundedCachingCost,
    storageCost: roundedStorageCost,
    totalCost: totalCost
  };
}

// Helper function to get a GoogleGenAI client instance using NEW SDK structure
function _getGenAIClient(apiKeyOrGenAIInstance: string | GoogleGenAI): GoogleGenAI {
  if (apiKeyOrGenAIInstance instanceof GoogleGenAI) {
    return apiKeyOrGenAIInstance;
  }
  // At this point, apiKeyOrGenAIInstance must be a string.
  // The constructor requires an API key.
  if (apiKeyOrGenAIInstance.trim() === '') {
      // This case should ideally be prevented by callers, but as a safeguard:
      throw new Error("GoogleGenAI client initialization failed: API key cannot be an empty string.");
  }
  // NEW SDK: Use the updated constructor structure
  return new GoogleGenAI({ apiKey: apiKeyOrGenAIInstance });
}

export async function runLlmTask<TRequest extends LlmTaskRequest, TResult>(
  apiKeyOrGenAIInstance: string | GoogleGenAI,
  request: TRequest,
  useCaching: boolean = true, // Kept for signature, but direct caching is stubbed
  taskDef: TaskDefinition<TRequest, TResult> // Now non-optional
): Promise<LlmTaskResponse> {

  // Initialize the GoogleGenAI client using the helper function.
  // apiKeyOrGenAIInstance is guaranteed by its type (string | GoogleGenAI) to be provided.
  // _getGenAIClient handles whether it's a string key or an existing instance.
  const genAI: GoogleGenAI = _getGenAIClient(apiKeyOrGenAIInstance);

  const modelName = MODEL_NAME;

  const messageTextForPrompt = request.messages.map((msg, index) =>
    `${index}: [${msg.timestamp?.toISOString() || 'No Timestamp'}] ${msg.sender}: ${msg.text}`
  ).join('\n');

  // Always use TaskDefinition for prompt instruction
  if (typeof taskDef.getStandalonePromptInstruction !== 'function') {
    console.error(`Task ${request.task} called runLlmTask without a valid getStandalonePromptInstruction method in its TaskDefinition.`);
    return { success: false, error: `TaskDefinition issue for ${request.task}` };
  }
  const instruction: string = taskDef.getStandalonePromptInstruction(request);

  // 🔧 KEY FIX: Conditional prompt building based on cache status
  let fullPrompt: string;

  if (useCaching && activeCache?.name) {
    // ✅ FIXED: Cache contains messages, only send task instruction
    console.log(`✓ Using cache ${activeCache.name} for task ${request.task} - sending ONLY task instruction`);
    fullPrompt = instruction;
  } else {
    // ✅ FIXED: No cache - send full messages + instruction
    console.log(`⚠ No cache available for task ${request.task} - sending full messages + instruction`);
    fullPrompt = `You are a chat analysis assistant. Analyze the following chat messages provided.
Participants: ${request.participants?.map(p => `${p.name} (ID: ${p.id})`).join(', ') || 'Unknown'}
Messages:\n${messageTextForPrompt}\n\n${instruction}`;
  }

  // Estimate and truncate if needed to prevent token limit errors (only when not using cache)
  const estimatedTokens = Math.ceil(fullPrompt.length / 4); // Rough estimation: 4 chars per token
  if (estimatedTokens > MAX_INPUT_TOKENS && (!useCaching || !activeCache?.name)) {
    console.log(`Input text exceeds ${MAX_INPUT_TOKENS} token limit (${estimatedTokens} estimated tokens). Applying intelligent truncation...`);

    // Calculate how much we need to cut from messages
    const basePromptLength = fullPrompt.length - messageTextForPrompt.length;
    const maxMessageLength = (MAX_INPUT_TOKENS * 4) - basePromptLength - 1000; // Leave some buffer
    
    if (maxMessageLength > 0) {
      // Take the most recent messages that fit within the limit
      let truncatedMessages = messageTextForPrompt;
      if (messageTextForPrompt.length > maxMessageLength) {
        // Try to truncate intelligently by taking recent messages
        const messages = request.messages;
        let selectedMessages = [...messages];
        let currentLength = messageTextForPrompt.length;
        
        // Remove oldest messages until we fit
        while (currentLength > maxMessageLength && selectedMessages.length > 100) {
          selectedMessages = selectedMessages.slice(Math.ceil(selectedMessages.length * 0.1)); // Remove 10% from start
          const newMessageText = selectedMessages.map((msg, index) =>
            `${index}: [${msg.timestamp?.toISOString() || 'No Timestamp'}] ${msg.sender}: ${msg.text}`
          ).join('\n');
          currentLength = newMessageText.length;
          truncatedMessages = newMessageText;
        }
        
        // If still too long, do simple truncation
        if (currentLength > maxMessageLength) {
          truncatedMessages = messageTextForPrompt.substring(messageTextForPrompt.length - maxMessageLength);
        }
      }
      
      fullPrompt = `You are a chat analysis assistant. Analyze the following chat messages provided.
Participants: ${request.participants?.map(p => `${p.name} (ID: ${p.id})`).join(', ') || 'Unknown'}
Messages:\n${truncatedMessages}\n\n${instruction}`;
      
      console.log(`✓ Successfully truncated input from ${estimatedTokens} to ~${Math.ceil(fullPrompt.length / 4)} estimated tokens (${((Math.ceil(fullPrompt.length / 4) / MAX_INPUT_TOKENS) * 100).toFixed(1)}% of limit used)`);
    } else {
      return { success: false, error: "Input text too large even after truncation. Please use a smaller dataset." };
    }
  }

  let inputTokenCount = 0; // Will be calculated
  let outputTokenCount = 0; // Will be calculated
  let rawResponseText: string = "{}"; // Default to empty JSON object string
  let processedResult: TResult | null = null;
  let actualCachedTokensUsed = 0;

  try {
    // NEW SDK: Use the updated API structure

    // Calculate input tokens (simplified with new SDK)
    try {
      // Note: Token counting might have different API in new SDK
      inputTokenCount = Math.ceil(fullPrompt.length / 4); // Rough estimation for now
    } catch (tokenError: any) {
      console.warn(`Failed to count input tokens for task ${request.task}: ${tokenError.message}. Using estimated 0.`);
    }

    // NEW SDK: Prepare request using the updated structure
    let generationResult: any;

    if (useCaching && activeCache?.name) {
      // Use cache with new SDK structure
      console.log(`Using cache ${activeCache.name} for task ${request.task}`);
      generationResult = await retryWithBackoff(
        async () => genAI.models.generateContent({
          model: modelName,
          contents: fullPrompt, // NEW SDK: Direct string for contents
          config: {
            cachedContent: activeCache.name
          }
        }),
        3,
        BACKOFF_INITIAL_MS
      );
    } else {
      // No cache - use standard generation
      generationResult = await retryWithBackoff(
        async () => genAI.models.generateContent({
          model: modelName,
          contents: fullPrompt // NEW SDK: Direct string for contents
        }),
        3,
        BACKOFF_INITIAL_MS
      );
    }

    // NEW SDK: Simplified response parsing
    // The new SDK returns response.text directly
    if (generationResult.text) {
        rawResponseText = generationResult.text;
        console.log(`✅ Task ${request.task} received response: ${rawResponseText.length} characters`);
    } else {
        console.warn(`LLM call for task ${request.task} returned no text. Full result:`, generationResult);
        rawResponseText = "{}"; // Default empty JSON
    }

    // NEW SDK: Calculate output tokens and cache usage
    if (generationResult.usageMetadata) {
      const totalPromptTokens = generationResult.usageMetadata.promptTokenCount || inputTokenCount;
      outputTokenCount = generationResult.usageMetadata.candidatesTokenCount || 0;
      actualCachedTokensUsed = generationResult.usageMetadata.cachedContentTokenCount || 0;

      // 🔧 FIX: Calculate actual NEW input tokens (excluding cached tokens)
      if (actualCachedTokensUsed > 0) {
        // When using cache, input tokens = total prompt tokens - cached tokens
        inputTokenCount = Math.max(0, totalPromptTokens - actualCachedTokensUsed);
        console.log(`✅ Task ${request.task} utilized ${actualCachedTokensUsed} tokens from cache`);
        console.log(`💰 Input cost reduced: ${totalPromptTokens} total → ${inputTokenCount} new tokens (${actualCachedTokensUsed} from cache)`);
        console.log(`📊 Cache efficiency: ${((actualCachedTokensUsed / (generationResult.usageMetadata.totalTokenCount || 1)) * 100).toFixed(1)}%`);
      } else {
        // No cache used, use total prompt tokens
        inputTokenCount = totalPromptTokens;
      }
    } else {
      // Fallback: estimate output tokens
      outputTokenCount = Math.ceil(rawResponseText.length / 4);
    }


    let jsonStringToParse = rawResponseText.trim();
    
    // Regex to find JSON content within ```json ... ``` or ``` ... ```
    const markdownFenceRegex = /^```(?:json)?\s*([\s\S]*?)\s*```$/;
    const match = jsonStringToParse.match(markdownFenceRegex);

    if (match && match[1]) {
      jsonStringToParse = match[1].trim();
    }
    // If it wasn't in a markdown block, jsonStringToParse remains the original rawResponseText.trim()

    let llmJson: any; // Explicitly type as 'any' since we don't know the exact structure
    try {
      // Import the jsonFixer utility
      const { safeLlmJsonParse } = require('./utils/jsonFixer');
      llmJson = safeLlmJsonParse(jsonStringToParse);
      
      if (!llmJson) {
        // First attempt failed, try using LLM to fix the JSON
        console.log(`Initial JSON parsing failed for task ${request.task}, attempting LLM-based repair...`);
        
        // Prepare a prompt for the LLM to fix the JSON
        const fixJsonPrompt = `You are a JSON repair assistant. The following is a malformed JSON that needs to be fixed.
ONLY respond with the corrected, valid JSON. Do not include ANY explanations, comments, or markdown formatting.
Ensure all property names and string values use double quotes, not single quotes.
Make sure there are no trailing commas in arrays or objects.

${jsonStringToParse}`;
        
        // Make a new LLM call to fix the JSON
        const fixRequestPayload = {
          model: modelName,
          contents: [{ role: "user", parts: [{ text: fixJsonPrompt }] }],
          generationConfig: {
            temperature: 0.1, // Lower temperature for more deterministic output
            maxOutputTokens: 8192 // Ensure enough tokens for the full JSON
          }
        };
        
        const fixResult = await retryWithBackoff(
          async () => genAI.models.generateContent(fixRequestPayload),
          3,
          BACKOFF_INITIAL_MS
        );
        
        if (fixResult.candidates && fixResult.candidates.length > 0) {
          const fixedJsonString = fixResult.candidates?.[0]?.content?.parts?.[0]?.text || "{}";
          
          // Try parsing the LLM-fixed JSON
          try {
            // First try direct parsing
            try {
              llmJson = JSON.parse(fixedJsonString);
              console.log(`Successfully fixed JSON using LLM for task ${request.task}`);
            } catch (directParseError) {
              // If direct parsing fails, try one more cleanup
              const cleanedFixedJson = fixedJsonString
                .replace(/```json|```/g, '') // Remove markdown code blocks
                .trim()
                .replace(/,(\s*[\]}])/g, '$1'); // Remove trailing commas
              
              llmJson = JSON.parse(cleanedFixedJson);
              console.log(`Successfully fixed JSON using LLM and additional cleanup for task ${request.task}`);
            }
          } catch (fixError) {
            // If all parsing attempts fail, try rerunning the task with lower temperature
            console.error(`Both jsonFixer and LLM repair failed to fix JSON for task ${request.task}, attempting to rerun the task...`);
            
            try {
              // Create a new request with lower temperature to get cleaner output
              const rerunRequestPayload = {
                model: modelName,
                contents: fullPrompt, // NEW SDK: Direct string for contents
                config: {
                  temperature: 0.1 // Lower temperature for more deterministic output
                }
              };
              
              console.log(`Rerunning task ${request.task} with temperature ${rerunRequestPayload.config.temperature}`);
              
              // Rerun the task
              const rerunResult = await retryWithBackoff(
                async () => genAI.models.generateContent(rerunRequestPayload),
                2,
                BACKOFF_INITIAL_MS
              );
              
              if (rerunResult.text) {
                const rerunResponseText = rerunResult.text;

                // Extract JSON from the rerun response
                const rerunJsonString = rerunResponseText.trim().replace(/^```(?:json)?\s*([\s\S]*?)\s*```$/, '$1').trim();
                
                try {
                  llmJson = JSON.parse(rerunJsonString);
                  console.log(`Successfully parsed JSON after rerunning task ${request.task}`);
                } catch (rerunParseError) {
                  // If rerun also fails, create a minimal valid result
                  console.error(`Rerun also failed to produce valid JSON for task ${request.task}`);
                  
                  // Create a minimal valid result based on the task
                  if (request.task.includes("cringe")) {
                    // For cringe moments, create an empty result with participant names
                    llmJson = {};
                    (request.participants || []).forEach(p => {
                      llmJson[p.name] = [];
                    });
                  } else {
                    // For other tasks, create an empty array or object
                    llmJson = request.task.includes("dynamics") || request.task.includes("ranking") ? [] : {};
                  }
                }
              } else {
                throw new Error(`Rerun failed to generate a response for task ${request.task}`);
              }
            } catch (rerunError) {
              console.error(`Error during task rerun for ${request.task}:`, rerunError);
              
              // Create a minimal valid result based on the task
              if (request.task.includes("cringe")) {
                // For cringe moments, create an empty result with participant names
                llmJson = {};
                (request.participants || []).forEach(p => {
                  llmJson[p.name] = [];
                });
              } else {
                // For other tasks, create an empty array or object
                llmJson = request.task.includes("dynamics") || request.task.includes("ranking") ? [] : {};
              }
            }
          }
        } else {
          throw new Error(`LLM repair failed to generate a response for task ${request.task}`);
        }
      } else {
        console.log(`Successfully parsed JSON for task ${request.task}`);
      }
    } catch (parseError) {
      console.error(`Failed to parse JSON for task ${request.task}. Error: ${(parseError as Error).message}`);
      console.error(`Raw JSON string (first 200 chars): ${jsonStringToParse.substring(0, 200)}...`);
      
      // Create a minimal valid result to prevent complete failure
      if (request.task.includes("cringe")) {
        // For cringe moments, create an empty result with participant names
        llmJson = {};
        (request.participants || []).forEach(p => {
          llmJson[p.name] = [];
        });
      } else {
        // For other tasks, create an empty array or object
        llmJson = request.task.includes("dynamics") || request.task.includes("ranking") ? [] : {};
      }
    }

    if (typeof taskDef.processStandaloneResponse !== 'function') {
        console.error(`Task ${request.task} called runLlmTask without a valid processStandaloneResponse method in its TaskDefinition.`);
        return { success: false, error: `TaskDefinition processing issue for ${request.task}`, rawResponse: rawResponseText };
    }
    processedResult = taskDef.processStandaloneResponse(llmJson, request);

    return {
      success: true, result: processedResult, rawResponse: rawResponseText,
      tokenUsage: {
        inputTokens: inputTokenCount, outputTokens: outputTokenCount, totalTokens: inputTokenCount + outputTokenCount,
        cost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).totalCost,
        inputCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).inputCost,
        outputCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).outputCost,
        cachingCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).cachingCost,
        storageCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).storageCost,
        cachedContentTokenUsage: actualCachedTokensUsed,
      }
    };
  } catch (error: any) {
    console.error(`Error in runLlmTask for task ${request.task}:`, error.message);
    if (rawResponseText) {
        console.error("Raw LLM response (if any) was:", rawResponseText.substring(0, 500) + (rawResponseText.length > 500 ? "..." : ""));
    }
    return {
      success: false, error: error.message, rawResponse: rawResponseText,
      tokenUsage: {
        inputTokens: inputTokenCount, outputTokens: outputTokenCount, totalTokens: inputTokenCount + outputTokenCount,
        cost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).totalCost,
        inputCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).inputCost,
        outputCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).outputCost,
        cachingCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).cachingCost,
        storageCost: calculateTokenCost(inputTokenCount, outputTokenCount, (useCaching && !!activeCache && actualCachedTokensUsed > 0), actualCachedTokensUsed, 1).storageCost,
        cachedContentTokenUsage: actualCachedTokensUsed,
      }
    };
  }
}

export async function runLlmBatchTasks(
  apiKeyForCheck: string | undefined,
  batchTasks: LlmBatchTask[],
  useCaching: boolean = true
): Promise<Record<string, LlmTaskResponse>> {
  const finalResults: Record<string, LlmTaskResponse> = {};

  const apiKeyIsConfigured = !!apiKeyForCheck;

  if (!apiKeyIsConfigured) {
    console.error("No API key configured for runLlmBatchTasks.");
    const errorResponse: LlmTaskResponse = { success: false, error: "API key not configured." };
    batchTasks.forEach(task => finalResults[task.taskId] = errorResponse);
    return finalResults;
  }

  if (batchTasks.length === 0) return {};
  console.log(`Received ${batchTasks.length} LLM tasks. Executing individually using TaskDefinitions...`);

  const genAIClientForBatch = _getGenAIClient(apiKeyForCheck);

  let activityMetrics: ActivityMetrics | undefined;
  let sentimentMetrics: SentimentMetrics | undefined;
  const commonMessages = batchTasks[0]?.request.messages;

  const needsParticipantLevelProcessing = batchTasks.some(bt => {
    const taskDef = getTaskDefinition(bt.request.task);
    return (taskDef && (taskDef.taskName.includes("person") || taskDef.taskName.includes("participant")));
    // Simplified: Rely on taskDef name for now. Add PARTICIPANT_LEVEL_TASKS check if needed.
  });

  if (needsParticipantLevelProcessing && commonMessages && commonMessages.length > 0) {
    activityMetrics = analyzeActivity(commonMessages);
    sentimentMetrics = analyzeSentiment(commonMessages);
  }

  // 🚀 HYBRID CACHE EXECUTION: Create cache once + Parallel execution
  console.log(`🔄 Starting hybrid cache execution for ${batchTasks.length} tasks...`);

  // Step 1: Create cache if using caching and we have messages
  let cacheCreationCost = 0;
  let cacheTokensAtCreation = 0; // Track actual cache size at creation time
  if (useCaching && commonMessages && commonMessages.length > 0) {
    const participants = batchTasks[0]?.request.participants || [];
    console.log('🗄️ Creating shared cache for all batch tasks...');
    const cacheResult = await manageChatCache(apiKeyForCheck, 'create', commonMessages, participants);
    if (typeof cacheResult === 'object' && 'success' in cacheResult && cacheResult.success) {
      console.log('✅ Shared cache created successfully - ready for parallel execution');
      // FIX: Use the actual token count returned from cache creation
      cacheTokensAtCreation = cacheResult.tokenCount;
      cacheCreationCost = (cacheTokensAtCreation / 1000000) * INPUT_TOKEN_COST_PER_MILLION;
      console.log(`💰 Cache creation cost: $${cacheCreationCost.toFixed(6)} for ${cacheTokensAtCreation} tokens (actual cache size)`);
    } else {
      console.warn('⚠️ Failed to create cache, proceeding without caching');
      useCaching = false;
    }
  }

  // Step 2: Execute all tasks in PARALLEL using the shared cache
  console.log(`🚀 Executing ${batchTasks.length} tasks in PARALLEL with shared cache...`);

  const taskPromises = batchTasks.map(async (batchTask, index) => {
    console.log(`[${index+1}/${batchTasks.length}] Starting parallel task ${batchTask.taskId} (${batchTask.request.task})...`);

    const taskDefinition = getTaskDefinition(batchTask.request.task);
    let response: LlmTaskResponse;

    if (taskDefinition) {
      try {
        response = await runLlmTask(
          genAIClientForBatch,
          batchTask.request,
          useCaching,
          taskDefinition
        );

        // Additional validation to ensure we have a valid response
        if (response.success && response.result) {
          // Check if result is empty object or array
          const isEmpty =
            (Array.isArray(response.result) && response.result.length === 0) ||
            (typeof response.result === 'object' &&
             response.result !== null &&
             Object.keys(response.result).length === 0);

          if (isEmpty) {
            console.warn(`Task ${batchTask.request.task} (ID: ${batchTask.taskId}) returned empty result`);
            // Create a fallback result based on task type
            if (batchTask.request.task.includes("cringe")) {
              // For cringe moments, create an empty result with participant names
              const fallbackResult: { [key: string]: any[] } = {};
              (batchTask.request.participants || []).forEach(p => {
                fallbackResult[p.name] = [];
              });
              response.result = fallbackResult;
            }
          }
        }

        console.log(`✅ Parallel task ${batchTask.taskId} completed: ${response.success ? 'Success' : 'Failed'}`);
        if (response.tokenUsage) {
          console.log(`   Tokens: ${response.tokenUsage.totalTokens}, Cost: $${response.tokenUsage.cost.toFixed(6)}`);
          if (response.tokenUsage.cachedContentTokenUsage && response.tokenUsage.cachedContentTokenUsage > 0) {
            console.log(`   Cache used: ${response.tokenUsage.cachedContentTokenUsage} tokens`);
          }
        }

      } catch (e: any) {
        console.error(`Error executing parallel task ${batchTask.request.task} (ID: ${batchTask.taskId}) via runLlmTask:`, e.message);
        response = {
          success: false,
          error: e.message,
          // Include a minimal valid result to prevent downstream errors
          result: batchTask.request.task.includes("dynamics") || batchTask.request.task.includes("ranking") ? [] : {}
        };
      }
    } else {
      console.error(`No TaskDefinition found for task: ${batchTask.request.task} (ID: ${batchTask.taskId})`);
      response = {
        success: false,
        error: `No TaskDefinition for task: ${batchTask.request.task}`,
        // Include a minimal valid result to prevent downstream errors
        result: batchTask.request.task.includes("dynamics") || batchTask.request.task.includes("ranking") ? [] : {}
      };
    }

    return { taskId: batchTask.taskId, response };
  });

  // Step 3: Wait for all parallel tasks to complete
  try {
    console.log('⏳ Waiting for all parallel tasks to complete...');
    const taskResults = await Promise.all(taskPromises);

    // Process results and add cache creation cost to first task
    taskResults.forEach(({ taskId, response }, index) => {
      // Add cache creation cost to the first task only
      if (index === 0 && cacheCreationCost > 0 && response.tokenUsage) {
        console.log(`💰 Adding cache creation cost $${cacheCreationCost.toFixed(6)} to first task ${taskId}`);
        console.log(`🔍 Cache creation: ${cacheTokensAtCreation} tokens, Cache access per task: ${response.tokenUsage.cachedContentTokenUsage || 0} tokens`);
        response.tokenUsage.cachingCost = (response.tokenUsage.cachingCost || 0) + cacheCreationCost;
        response.tokenUsage.cost = (response.tokenUsage.cost || 0) + cacheCreationCost;
      }
      finalResults[taskId] = response;
    });

    console.log(`✅ Completed all ${batchTasks.length} tasks in PARALLEL with shared cache`);

    // --- NEW: Structure Error Retry Logic ---
    const structureErrorPattern = /LLM result for .* did not match expected structure/;
    const tasksToRetry: LlmBatchTask[] = [];

    // Identify tasks with structure errors
    taskResults.forEach(({ taskId, response }) => {
      if (!response.success && response.error && structureErrorPattern.test(response.error)) {
        console.warn(`🔄 Task ${taskId} failed with structure error, will retry: ${response.error}`);
        const originalTask = batchTasks.find(task => task.taskId === taskId);
        if (originalTask) {
          tasksToRetry.push(originalTask);
        }
      }
    });

    // Retry failed tasks if any exist
    if (tasksToRetry.length > 0) {
      console.log(`🔄 Starting retry for ${tasksToRetry.length} tasks with structure errors...`);

      // Brief delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 1500));

      try {
        // Retry with same cache if still active
        const retryPromises = tasksToRetry.map(async (retryTask, index) => {
          console.log(`[RETRY ${index+1}/${tasksToRetry.length}] Retrying task ${retryTask.taskId} (${retryTask.request.task})...`);

          const taskDefinition = getTaskDefinition(retryTask.request.task);
          let retryResponse: LlmTaskResponse;

          if (taskDefinition) {
            try {
              retryResponse = await runLlmTask(
                genAIClientForBatch,
                retryTask.request,
                useCaching && !!activeCache, // Use cache if still available
                taskDefinition
              );

              console.log(`✅ Retry task ${retryTask.taskId}: ${retryResponse.success ? 'Success' : 'Failed'}`);
              if (!retryResponse.success) {
                console.warn(`   Retry error: ${retryResponse.error}`);
              }
            } catch (e: any) {
              console.error(`❌ Retry failed for task ${retryTask.taskId}:`, e.message);
              retryResponse = {
                success: false,
                error: `Retry failed: ${e.message}`,
                result: retryTask.request.task.includes("dynamics") || retryTask.request.task.includes("ranking") ? [] : {}
              };
            }
          } else {
            retryResponse = {
              success: false,
              error: `No TaskDefinition for retry task: ${retryTask.request.task}`,
              result: retryTask.request.task.includes("dynamics") || retryTask.request.task.includes("ranking") ? [] : {}
            };
          }

          return { taskId: retryTask.taskId, response: retryResponse };
        });

        // Wait for all retries to complete
        const retryResults = await Promise.all(retryPromises);

        // Update final results with retry outcomes
        retryResults.forEach(({ taskId, response }) => {
          console.log(`🔄 Updating result for task ${taskId}: ${response.success ? 'Success' : 'Still Failed'}`);
          finalResults[taskId] = response; // Replace original failed result with retry result
        });

        console.log(`✅ Retry completed for ${tasksToRetry.length} tasks`);
      } catch (retryError: any) {
        console.error("❌ Error during retry execution:", retryError.message);
      }
    }

  } catch (batchError: any) {
    console.error("Error during parallel execution:", batchError.message);
    // Handle any tasks that might have failed
    batchTasks.forEach(task => {
      if (!finalResults[task.taskId]) {
        finalResults[task.taskId] = { success: false, error: `Parallel execution error: ${batchError.message}` };
      }
    });
  }

  if (useCaching && activeCache && genAIClientForBatch) {
    await deleteCache(genAIClientForBatch);
  }

  return finalResults;
}

export async function manageChatCache(
  apiKeyForCheck: string,
  action: 'create' | 'delete' | 'status',
  messages?: ParsedMessage[],
  participants?: { id: string; name: string }[]
): Promise<boolean | CacheStatus | { success: boolean; tokenCount: number }> {
  const apiKeyIsConfigured = !!apiKeyForCheck;
  if (!apiKeyIsConfigured) { console.error("API key missing for manageChatCache"); return false;}

  const genAI = _getGenAIClient(apiKeyForCheck);
  const modelName = MODEL_NAME;

  if (action === 'create') {
    if (!messages || !participants) return { success: false, tokenCount: 0 };
    if (activeCache) await deleteCache(genAI);
    const cacheResult = await createChatCache(genAI, messages, participants, modelName);
    activeCache = cacheResult.cache;
    return { success: !!activeCache, tokenCount: cacheResult.tokenCount };
  } else if (action === 'delete') {
    return deleteCache(genAI);
  } else if (action === 'status') {
    return getCacheStatus();
  }
  return false;
}

export function getCacheStatus(): CacheStatus {
  return {
    active: !!activeCache,
    cacheName: activeCache?.name
  };
}

/**
 * Get the current cache token limit
 */
export function getCacheTokenLimit(): number {
  return MAX_REASONABLE_TOKENS;
}

/**
 * Check if cache creation would be limited by token count
 */
export function wouldCacheBeLimit(messages: ParsedMessage[], participants: { id: string; name: string }[]): {
  wouldLimit: boolean;
  estimatedTokens: number;
  limit: number;
  messageCount: number;
} {
  const messageText = messages.map((msg, index) =>
    `${index}: [${msg.timestamp?.toISOString() || 'No Timestamp'}] ${msg.sender}: ${msg.text}`
  ).join('\n');

  const participantText = `Participants: ${participants?.map(p => `${p.name} (ID: ${p.id})`).join(', ') || 'Unknown'}`;
  const baseContent = `Chat Analysis Data:\n\n${participantText}\n\nMessages:\n`;
  const fullContent = `${baseContent}${messageText}`;

  const estimatedTokens = Math.ceil((fullContent.length + 200) / 5.5);

  return {
    wouldLimit: estimatedTokens > MAX_REASONABLE_TOKENS,
    estimatedTokens,
    limit: MAX_REASONABLE_TOKENS,
    messageCount: messages.length
  };
}

// Ensure these sets are defined if referenced above, though they are not used in the stubbed version.
// const GROUP_LEVEL_TASKS = new Set([...]);
// const PARTICIPANT_LEVEL_TASKS = new Set([...]);
// const PAIR_LEVEL_TASKS = new Set([...]);





