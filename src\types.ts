/**
 * Types Module
 *
 * This module contains type definitions used throughout the application.
 */

/**
 * Cloudflare bindings expected from wrangler.toml
 */
export type CloudflareBindings = {
  // Optional bindings that may not be available
  ANALYSIS_QUEUE?: Queue;
  ANALYSIS_KV?: KVNamespace;
  ANALYSIS_R2?: R2Bucket;
  GEMINI_API_KEY: string; // Environment variable for Gemini API Key
  
  // SaaS transformation additions
  DEVICE_QUOTA: DurableObjectNamespace;           // Device quota management
  TRANSACTION_CREDITS: DurableObjectNamespace;    // Transaction-based credit system (legacy)
  SUBSCRIPTION_STORE: DurableObjectNamespace;     // New subscription-based system
  COST_TRACKER?: DurableObjectNamespace;          // Cost tracking system (optional)
  JWT_PRIVATE_KEY?: string;                       // For signing JWTs (optional for simplified approach)
  
  // RevenueCat configurations
  REVENUECAT_SECRET_KEY?: string;                 // RevenueCat REST API secret key (optional during migration)
  REVENUECAT_AUTH_TOKEN?: string;                 // RevenueCat webhook auth token
  
  ENVIRONMENT?: "development" | "staging" | "production"; // Deployment environment
};

/**
 * Device quota state stored in Durable Object
 */
export interface DeviceState {
  deviceId: string;                 // RevenueCat anonymous user ID (device identifier)
  revenueCatUserId?: string;        // RevenueCat's anonymous user ID (same as deviceId)
  tier: 'free' | 'premium';         // Subscription tier (for backward compatibility)
  totalUsed: number;                // Total analyses used (for stats)
  resetDate: string;                // Reset date (legacy)
  linkedTransactionIds: string[];   // Apple Transaction IDs linked to this device (legacy)
  activeSubscriptionId?: string;    // Current active subscription ID (new subscription system)
  freeAnalysisUsed: boolean;        // Has used their 1 free analysis
  createdAt: number;                // Device registration timestamp
  lastActivity: number;             // Last analysis timestamp
  variantKey?: string;              // A/B testing variant (future use)
  
  // Enhanced free user registration tracking
  registered_with_revenuecat: boolean;  // Whether user is registered with RevenueCat servers
  registration_attempts: number;        // Number of registration attempts
  last_registration_attempt: number;   // Timestamp of last registration attempt
  registration_source: 'app' | 'webhook'; // How the registration occurred
}

/**
 * Quota limits per tier
 */
export const QUOTA_LIMITS = {
  free: {
    totalAnalyses: 1        // 1 free analysis total (lifetime)
  },
  premium: {
    dailyAnalyses: 1      // 1 daily credit (configured in credits.ts)
  }
} as const;

/**
 * Subscription record stored in SubscriptionStore
 */
export interface SubscriptionRecord {
  subscription_id: string;          // RevenueCat original_transaction_id
  transaction_id: string;           // Current transaction_id
  device_id: string;                // Device identifier
  started_at: number;               // Period start timestamp
  expires_at: number;               // Period end timestamp
  period_type: 'TRIAL' | 'NORMAL'; // Trial vs paid subscription
  analyses_used: number;            // Analyses used in current period
  analyses_limit: number;           // Max analyses per period (legacy field, now represents daily limit)
  is_active: boolean;               // Current status
  auto_renew: boolean;              // Renewal status
  product_id: string;               // RevenueCat product identifier
  store?: string;                   // Store where purchase was made (e.g., 'app_store', 'play_store')
  is_sandbox?: boolean;             // Whether this is a sandbox/test purchase
  daily_credits: number;            // Credits granted per day (new field)
  daily_used_today: number;        // Credits used today (new field)
  last_daily_reset: number;        // Last time daily counter was reset (new field)
}

/**
 * Subscription status information
 */
export interface SubscriptionStatus {
  has_active_subscription: boolean;
  analyses_remaining: number;
  expires_at: number;
  period_type: 'TRIAL' | 'NORMAL';
  days_remaining: number;
  is_trial: boolean;
  subscription_id?: string;
}

/**
 * API response for quota check
 */
export interface QuotaResponse {
  allowed: boolean;
  remaining: number;
  tier: 'free' | 'premium';
  resetDate: string;
  message?: string;
  subscription_status?: SubscriptionStatus;  // New subscription information
}
