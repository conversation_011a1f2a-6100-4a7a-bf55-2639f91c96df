#!/usr/bin/env tsx

/**
 * Simple CLI script to convert Unicode escape sequences in JSON files
 * Usage: npx tsx convert-unicode.ts <input-file> [output-file]
 */

import { UnicodeReverter } from './unicode-reverter';

async function main() {
  const args = process.argv.slice(2);
  
  if (args.length === 0) {
    console.log(`
🔄 Unicode Converter for Instagram JSON Files

Converts Unicode escape sequences (like \\u00c3\\u0096) back to actual characters.
Perfect for Instagram exports with Turkish, Arabic, emoji, and other non-English content.

Usage:
  npx tsx convert-unicode.ts <input-file> [output-file]
  
Arguments:
  input-file   : Path to the JSON file with Unicode escapes
  output-file  : Optional output file path (default: adds '_converted' suffix)
  
Examples:
  npx tsx convert-unicode.ts message_1_3.json
  npx tsx convert-unicode.ts message_1_3.json clean_messages.json
  
Features:
  ✅ Turkish characters (ö, ü, ş, ğ, ı, ç)
  ✅ Arabic/Kurdish text
  ✅ All emojis (😂, ❤️, 🥶, etc.)
  ✅ Special punctuation and symbols
  ✅ Preserves JSON structure
  ✅ Detailed conversion statistics
    `);
    process.exit(1);
  }

  const inputFile = args[0];
  const outputFile = args[1];

  console.log('🚀 Starting Unicode conversion...\n');

  try {
    const reverter = new UnicodeReverter({
      inputFile,
      outputFile,
      verbose: true
    });

    await reverter.processFile();
    
    console.log('\n✨ Conversion completed successfully!');
    console.log('📝 Your file now contains properly decoded Unicode characters.');
    
  } catch (error) {
    console.error('\n❌ Conversion failed:', error instanceof Error ? error.message : error);
    process.exit(1);
  }
}

// Run the main function
main().catch(console.error);
