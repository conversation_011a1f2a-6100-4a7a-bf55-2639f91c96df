/**
 * CostTracker Durable Object
 * Handles cost tracking with proper concurrency control
 */

export class CostTracker {
  private state: DurableObjectState;
  private totalCost: number = 0;
  private costEntries: Array<any> = [];
  private initialized: boolean = false;

  constructor(state: DurableObjectState) {
    this.state = state;
  }

  private async initialize() {
    if (this.initialized) return;
    
    // Load existing data from storage
    const totalCostData = await this.state.storage.get('total_cost');
    this.totalCost = totalCostData ? parseFloat(totalCostData as string) : 0;
    
    const entriesData = await this.state.storage.get('cost_entries');
    this.costEntries = entriesData ? JSON.parse(entriesData as string) : [];
    
    this.initialized = true;
  }

  async fetch(request: Request): Promise<Response> {
    await this.initialize();
    
    const url = new URL(request.url);
    const pathname = url.pathname;

    if (pathname === '/add-cost' && request.method === 'POST') {
      const { deviceId, totalCost, tokenUsage } = await request.json();
      
      // Add to total cost atomically
      this.totalCost += totalCost;
      
      // Add new entry (without exposing deviceId)
      const costEntry = {
        totalCost,
        tokenUsage,
        timestamp: Date.now(),
        date: new Date().toISOString()
      };
      
      this.costEntries.unshift(costEntry); // Add to beginning for newest first
      
      // Keep only last 1000 entries to avoid memory issues
      if (this.costEntries.length > 1000) {
        this.costEntries = this.costEntries.slice(0, 1000);
      }
      
      // Persist to storage
      await this.state.storage.put('total_cost', this.totalCost.toString());
      await this.state.storage.put('cost_entries', JSON.stringify(this.costEntries));
      
      return new Response(JSON.stringify({ 
        success: true, 
        newTotal: this.totalCost 
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    if (pathname === '/get-costs' && request.method === 'GET') {
      const limitParam = url.searchParams.get('limit');
      const limit = limitParam ? parseInt(limitParam) : 20;
      
      const recentCosts = this.costEntries.slice(0, limit);
      
      return new Response(JSON.stringify({
        totalCost: this.totalCost,
        recentCosts,
        lastUpdated: new Date().toISOString()
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response('Not found', { status: 404 });
  }
}