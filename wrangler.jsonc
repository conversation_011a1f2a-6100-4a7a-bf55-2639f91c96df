/**
 * For more details on how to configure Wrangler, refer to:
 * https://developers.cloudflare.com/workers/wrangler/configuration/
 */
{
	"$schema": "node_modules/wrangler/config-schema.json",
	"name": "chatbuster-backend",
	"main": "src/index.ts",
	"compatibility_date": "2025-04-30",
	"assets": {
		"binding": "ASSETS",
		"directory": "./public"
	},
	"observability": {
		"enabled": true
	},
	// --- KV Namespace Binding ---
	// Create a KV namespace in the Cloudflare dashboard:
	// `wrangler kv:namespace create ANALYSIS_KV`
	// Then add the `id` and `preview_id` below.
	// Commented out until proper KV namespace IDs are provided
	// "kv_namespaces": [
	//	{ "binding": "ANALYSIS_KV", "id": "YOUR_KV_NAMESPACE_ID", "preview_id": "YOUR_KV_PREVIEW_ID" }
	// ],
	// --- R2 Bucket Binding ---
	// Create an R2 bucket in the Cloudflare dashboard:
	// `wrangler r2 bucket create analysis-r2-bucket`
	// Then add the `bucket_name` below.
	// Commented out until proper R2 buckets are created
	// "r2_buckets": [
	//	{ "binding": "ANALYSIS_R2", "bucket_name": "analysis-r2-bucket", "preview_bucket_name": "analysis-r2-bucket-preview" }
	// ],
	// --- Queue Binding & Consumer ---
	// Queues are disabled as they require Enterprise-level Cloudflare account
	// "queues": {
	//	"producers": [
	//		{ "queue": "analysis-queue", "binding": "ANALYSIS_QUEUE" }
	//	],
	//	"consumers": [
	//		{ "queue": "analysis-queue" }
	//	]
	// },
	
	// --- Durable Objects for SaaS functionality ---
	"durable_objects": {
		"bindings": [
			{
				"name": "DEVICE_QUOTA",
				"class_name": "DeviceQuota"
			},
			{
				"name": "TRANSACTION_CREDITS",
				"class_name": "TransactionCreditsStore"
			},
			{
				"name": "SUBSCRIPTION_STORE",
				"class_name": "SubscriptionStore"
			},
			{
				"name": "COST_TRACKER",
				"class_name": "CostTracker"
			}
		]
	},
	
	// --- Durable Objects Migrations ---
	"migrations": [
		{
			"tag": "v1",
			"new_classes": [
				"DeviceQuota"
			]
		},
		{
			"tag": "v2",
			"new_classes": [
				"TransactionCreditsStore"
			]
		},
		{
			"tag": "v3",
			"new_classes": [
				"SubscriptionStore"
			]
		},
		{
			"tag": "v4",
			"new_classes": [
				"CostTracker"
			]
		}
	],
	/**
		* Smart Placement
	 * Docs: https://developers.cloudflare.com/workers/configuration/smart-placement/#smart-placement
	 */
	// "placement": { "mode": "smart" },

	/**
	 * Bindings
	 * Bindings allow your Worker to interact with resources on the Cloudflare Developer Platform, including
	 * databases, object storage, AI inference, real-time communication and more.
	 * https://developers.cloudflare.com/workers/runtime-apis/bindings/
	 */

	/**
	 * Environment Variables
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#environment-variables
	 */
	"vars": {
	 // Define environment variables here.
	 // For local dev (`wrangler dev`), set GEMINI_API_KEY in a .dev.vars file:
	 // GEMINI_API_KEY="YOUR_ACTUAL_API_KEY"
	 // For deployment, set it as a secret: `wrangler secret put GEMINI_API_KEY`
	 //"GEMINI_API_KEY": "PlaceHolder" // Placeholder, will be overridden by .dev.vars or secret
	},
	/**
	 * Note: Use secrets to store sensitive data.
	 * https://developers.cloudflare.com/workers/configuration/secrets/
	 */

	/**
	 * Static Assets
	 * https://developers.cloudflare.com/workers/static-assets/binding/
	 */
	// "assets": { "directory": "./public/", "binding": "ASSETS" },

	/**
	 * Service Bindings (communicate between multiple Workers)
	 * https://developers.cloudflare.com/workers/wrangler/configuration/#service-bindings
	 */
	// "services": [{ "binding": "MY_SERVICE", "service": "my-service" }]
}
