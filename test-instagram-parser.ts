import { parseInstagramJSON } from './src/parser';
import * as fs from 'fs';

console.log('Testing Instagram JSON Parser...');

// Test with message_1 2.json
const json1 = fs.readFileSync('message_1 2.json', 'utf8');
const result1 = parseInstagramJSON(json1);
console.log('\n=== Results from message_1 2.json ===');
console.log('Total messages:', result1.length);
console.log('First 3 messages:');
result1.slice(0, 3).forEach((msg, i) => {
  console.log(`${i+1}. [${msg.timestamp?.toISOString()}] ${msg.sender}: ${msg.text.substring(0, 50)}...`);
});

// Test with message_1 3.json  
const json2 = fs.readFileSync('message_1 3.json', 'utf8');
const result2 = parseInstagramJSON(json2);
console.log('\n=== Results from message_1 3.json ===');
console.log('Total messages:', result2.length);
console.log('First 3 messages:');
result2.slice(0, 3).forEach((msg, i) => {
  console.log(`${i+1}. [${msg.timestamp?.toISOString()}] ${msg.sender}: ${msg.text.substring(0, 50)}...`);
});

console.log('\n=== Test completed successfully! ===');