import { parseInstagram<PERSON>SO<PERSON> } from '../parser.js';

describe('Instagram JSON Parser', () => {
  const mockInstagramJSON = {
    "participants": [
      {
        "name": "legendofrusswestbrook23"
      },
      {
        "name": "<PERSON><PERSON>"
      },
      {
        "name": "<PERSON><PERSON>"
      },
      {
        "name": "<PERSON><PERSON>"
      },
      {
        "name": "<PERSON><PERSON>"
      },
      {
        "name": "arda"
      },
      {
        "name": "<PERSON><PERSON>"
      },
      {
        "name": "tuna olmez"
      }
    ],
    "messages": [
      {
        "sender_name": "<PERSON><PERSON>",
        "timestamp_ms": 1746065239600,
        "content": "Ben dc geldim",
        "is_geoblocked_for_viewer": false,
        "is_unsent_image_by_messenger_kid_parent": false
      },
      {
        "sender_name": "<PERSON><PERSON>",
        "timestamp_ms": 1746065235200,
        "content": "Abi burdan yazabilirsin",
        "is_geoblocked_for_viewer": false,
        "is_unsent_image_by_messenger_kid_parent": false
      },
      {
        "sender_name": "arda",
        "timestamp_ms": 1746065216280,
        "content": "Ya da burden mi yazcaz",
        "is_geoblocked_for_viewer": false,
        "is_unsent_image_by_messenger_kid_parent": false
      }
    ]
  };

  test('should parse Instagram JSON to ParsedMessage format', () => {
    const jsonString = JSON.stringify(mockInstagramJSON);
    const result = parseInstagramJSON(jsonString);

    expect(result).toHaveLength(3);
    expect(result[0]).toEqual({
      timestamp: new Date(1746065216280),
      sender: "arda",
      text: "Ya da burden mi yazcaz",
      isSystemMessage: false,
    });
    expect(result[1]).toEqual({
      timestamp: new Date(1746065235200),
      sender: "Deniz",
      text: "Abi burdan yazabilirsin",
      isSystemMessage: false,
    });
    expect(result[2]).toEqual({
      timestamp: new Date(1746065239600),
      sender: "Deniz",
      text: "Ben dc geldim",
      isSystemMessage: false,
    });
  });

  test('should detect system messages correctly', () => {
    const systemMessageJSON = {
      "participants": [
        {"name": "User1"},
        {"name": "User2"}
      ],
      "messages": [
        {
          "sender_name": "User1",
          "timestamp_ms": 1746065216280,
          "content": "User1 sent an attachment.",
          "is_geoblocked_for_viewer": false,
          "is_unsent_image_by_messenger_kid_parent": false
        },
        {
          "sender_name": "User2",
          "timestamp_ms": 1746065235200,
          "content": "user2 liked a message",
          "is_geoblocked_for_viewer": false,
          "is_unsent_image_by_messenger_kid_parent": false
        },
        {
          "sender_name": "User1",
          "timestamp_ms": 1746065239600,
          "content": "You sent an attachment.",
          "is_geoblocked_for_viewer": false,
          "is_unsent_image_by_messenger_kid_parent": false
        },
        {
          "sender_name": "User1",
          "timestamp_ms": 1746065240000,
          "content": "Normal message here",
          "is_geoblocked_for_viewer": false,
          "is_unsent_image_by_messenger_kid_parent": false
        }
      ]
    };

    const result = parseInstagramJSON(JSON.stringify(systemMessageJSON));
    
    expect(result).toHaveLength(4);
    // "sent an attachment" should keep original sender but mark as system message
    expect(result[0].isSystemMessage).toBe(true);
    expect(result[0].sender).toBe("User1");
    expect(result[0].text).toBe("User1 sent an attachment.");
    
    // "liked a message" should keep original sender but mark as system message  
    expect(result[1].isSystemMessage).toBe(true);
    expect(result[1].sender).toBe("User2");
    expect(result[1].text).toBe("user2 liked a message");
    
    // "You sent an attachment" should change sender to System
    expect(result[2].isSystemMessage).toBe(true);
    expect(result[2].sender).toBe("System");
    expect(result[2].text).toBe("You sent an attachment.");
    
    // Normal message should not be system message
    expect(result[3].isSystemMessage).toBe(false);
    expect(result[3].sender).toBe("User1");
    expect(result[3].text).toBe("Normal message here");
  });

  test('should filter out messages without content', () => {
    const jsonWithEmptyMessage = {
      ...mockInstagramJSON,
      messages: [
        ...mockInstagramJSON.messages,
        {
          "sender_name": "test",
          "timestamp_ms": 1746065240000,
          "is_geoblocked_for_viewer": false,
          "is_unsent_image_by_messenger_kid_parent": false
        }
      ]
    };

    const result = parseInstagramJSON(JSON.stringify(jsonWithEmptyMessage));
    expect(result).toHaveLength(3);
  });

  test('should throw error for invalid JSON', () => {
    expect(() => parseInstagramJSON('invalid json')).toThrow('Invalid Instagram JSON format');
  });

  test('should sort messages by timestamp', () => {
    const result = parseInstagramJSON(JSON.stringify(mockInstagramJSON));
    
    for (let i = 1; i < result.length; i++) {
      expect(result[i].timestamp!.getTime()).toBeGreaterThanOrEqual(result[i - 1].timestamp!.getTime());
    }
  });
});