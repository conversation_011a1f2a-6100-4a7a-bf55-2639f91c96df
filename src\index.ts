import { Hono } from "hono";
import { cors } from 'hono/cors';
import { nanoid } from 'nanoid'; // For generating unique IDs

// Import utilities
import { retry } from './utils/retry';
import { ensureCompleteAnalysisResult } from './utils/resultHelpers';
import { handleCacheStatus, handleCacheDelete } from './utils/cacheManager';

// Import SaaS utilities
import {
  extractDeviceId,
  checkAndChargeQuota,
  getQuotaStatus,
  checkRateLimit,
  estimateAnalysisCost
} from './utils/deviceUtils';


// Import types
import { CloudflareBindings } from './types';

// Import analysis modules
import { parseWhatsAppChat, parseInstagramJSON, parseChatFile, ParsedMessage } from './parser';
import { performFullAnalysis, getAvailableParticipants } from './analysis/orchestrator';
import { handleCharacterAnalysis } from './analysis/characterAnalysisHandler';

// Import Durable Objects
import { DeviceQuota } from './durableObjects/DeviceQuota';
import { TransactionCreditsStore } from './durableObjects/TransactionCreditsStore';
import { SubscriptionStore } from './durableObjects/SubscriptionStore';
import { CostTracker } from './durableObjects/CostTracker';

import { SubscriptionService } from './services/subscription'; // Added for debug endpoints
import { INTERNAL_ENDPOINTS } from './config/internalEndpoints'; // Added for debug endpoints

// import { checkQonversionSubscriptionStatus, createQonversionAPIClient } from './services/qonversionAPI'; // Removed
import { createRevenueCatAPIClient, RevenueCatWebhookEvent } from './services/revenuecatAPI';

const app = new Hono<{ Bindings: CloudflareBindings }>();

// Cost tracking storage - use CostTracker Durable Object for thread-safe operations
async function storeCostData(deviceId: string, totalCost: number, tokenUsage: any, env: CloudflareBindings) {
  try {
    console.log(`💾 storeCostData called: $${totalCost.toFixed(6)} for device ${deviceId.substring(0, 8)}...`);
    
    // Check if env is defined and has COST_TRACKER
    if (!env || !env.COST_TRACKER) {
      console.log(`❌ Cost tracking: $${totalCost.toFixed(6)} for device ${deviceId.substring(0, 8)}... (Cost tracker not available)`);
      return;
    }
    
    console.log(`💾 Getting CostTracker DO...`);
    // Use a single CostTracker instance for all cost tracking
    const costTrackerId = env.COST_TRACKER.idFromName('global');
    const costTracker = env.COST_TRACKER.get(costTrackerId);
    
    console.log(`💾 Sending data to CostTracker...`);
    const response = await costTracker.fetch(new Request('http://internal/add-cost', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        deviceId,
        totalCost,
        tokenUsage
      })
    }));
    
    console.log(`💾 CostTracker response status: ${response.status}`);
    
    if (response.ok) {
      const result = await response.json();
      // Type assertion to avoid 'unknown' error
      const costResult = result as { newTotal: number };
      console.log(`✅ Cost tracking: $${totalCost.toFixed(6)} added to total: $${costResult.newTotal.toFixed(6)}`);
    } else {
      const errorText = await response.text();
      console.error('❌ Failed to store cost data:', response.status, errorText);
    }
  } catch (error) {
    console.error('❌ Error storing cost data:', error);
  }
}

// Add environment variable validation middleware
app.use('*', async (c, next) => {
  // Log environment variable availability for debugging
  console.log('Environment variables check:');
  console.log(`GEMINI_API_KEY: ${c.env.GEMINI_API_KEY ? 'Available' : 'Missing'}`);
  // console.log(`QONVERSION_ACCESS_TOKEN: ${c.env.QONVERSION_ACCESS_TOKEN ? 'Available' : 'Missing'}`); // Removed
  // console.log(`QONVERSION_PROJECT_ID: ${c.env.QONVERSION_PROJECT_ID ? 'Available' : 'Missing'}`); // Removed
  
  // Validate critical environment variables
  // if (!c.env.QONVERSION_ACCESS_TOKEN || !c.env.QONVERSION_PROJECT_ID) { // Removed
  //   console.error("QONVERSION_ACCESS_TOKEN or QONVERSION_PROJECT_ID not configured - Qonversion features disabled"); // Removed
  // } // Removed
  if (!c.env.GEMINI_API_KEY) {
    console.error("GEMINI_API_KEY not configured");
  }
  
  await next();
});

// CORS disabled - allow all origins
app.use('*', cors({
  origin: '*',
  allowMethods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowHeaders: ['Content-Type', 'Authorization', 'Accept', 'X-Requested-With'],
  exposeHeaders: ['Content-Length', 'X-Kuma-Revision'],
  maxAge: 600,
  credentials: false,
}));

// --- New SaaS Endpoints ---

// Get quota status for a device
app.get("/quota/:deviceId", async (c) => {
  const deviceId = c.req.param('deviceId');
  
  // Basic validation
  if (!deviceId) {
    return c.json({ error: "Device ID is required" }, 400);
  }
  
  try {
    const quotaStatus = await getQuotaStatus(deviceId, c.env);
    return c.json(quotaStatus as any);
  } catch (error) {
    console.error("Error getting quota status:", error);
    return c.json({ error: "Failed to get quota status" }, 500);
  }
});

// Get quota status using Authorization header
app.get("/quota", async (c) => {
  const deviceId = extractDeviceId(c.req.raw);
  
  if (!deviceId) {
    return c.json({ error: "Valid device ID required in Authorization header" }, 401);
  }
  
  try {
    const quotaStatus = await getQuotaStatus(deviceId, c.env);
    return c.json(quotaStatus as any);
  } catch (error) {
    console.error("Error getting quota status:", error);
    return c.json({ error: "Failed to get quota status" }, 500);
  }
});

// Manual device registration endpoint (optional)
app.post("/register", async (c) => {
  try {
    const body = await c.req.json() as { deviceId: string };
    
    if (!body.deviceId) {
      return c.json({ error: "Device ID is required" }, 400);
    }
    
    // Validate device ID format (RevenueCat, Qonversion, and UUID for backward compatibility)
    // RevenueCat anonymous ID format: $RCAnonymousID:xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (32 hex chars)
    const revenueCatAnonymousRegex = /^\$RCAnonymousID:[0-9a-f]{32}$/i;
    // RevenueCat custom user ID format: can be any alphanumeric string
    const revenueCatCustomRegex = /^[a-zA-Z0-9_-]{3,}$/;
    // Qonversion format: QON_xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx (32 hex chars) or QNV:xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx (UUID format) // Removed
    // const qonversionRegex = /^(QON_[0-9a-f]{32}|QNV:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})$/i; // Removed
    // Fallback ID format for development/web
    const fallbackRegex = /^fallback_[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    // UUID format for backward compatibility
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    
    const isValidId = revenueCatAnonymousRegex.test(body.deviceId) ||
                      // qonversionRegex.test(body.deviceId) || // Removed
                      uuidRegex.test(body.deviceId) ||
                      fallbackRegex.test(body.deviceId) ||
                      (body.deviceId.length >= 3 && revenueCatCustomRegex.test(body.deviceId));
    
    if (!isValidId) {
      return c.json({ error: "Invalid device ID format. Must be RevenueCat anonymous ID ($RCAnonymousID:...), UUID, or custom alphanumeric ID." }, 400);
    }
    
    // Register the device
    const deviceQuotaId = c.env.DEVICE_QUOTA.idFromName(body.deviceId);
    const deviceQuota = c.env.DEVICE_QUOTA.get(deviceQuotaId);
    
    const response = await deviceQuota.fetch(new Request('http://internal/register', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ deviceId: body.deviceId })
    }));
    
    if (!response.ok) {
      throw new Error(`Registration failed: ${response.status}`);
    }
    
    const deviceState = await response.json() as any;
    console.log(`Device registered: ${body.deviceId}`);
    
    return c.json({
      success: true,
      deviceId: body.deviceId,
      tier: deviceState.tier,
      quotaRemaining: 0 // New users start with 0 analyses; entitlements come from subscriptions
    });
    
  } catch (error) {
    console.error("Device registration error:", error);
    return c.json({ error: "Registration failed" }, 500);
  }
});

// --- /participants Route ---
// Get available participants from a chat file for selection (used by love analysis)
app.post("/participants", async (c) => {
  try {
    console.log("Received /participants request");

    const formData = await c.req.formData();
    const file = formData.get("file") as File | null;

    if (!file) {
      return c.json({ error: "No file uploaded" }, 400);
    }

    // Read file content
    const fileName = file.name;
    const fileContent = await file.text();
    console.log(`Processing file: ${fileName} (${fileContent.length} characters)`);

    // Parse the chat file to extract participants
    let parsedMessages: ParsedMessage[];
    try {
      parsedMessages = parseChatFile(fileContent, fileName);
      
      if (parsedMessages.length === 0) {
        return c.json({
          error: "The chat file appears to be empty or in an unsupported format."
        }, 400);
      }
      
      console.log(`File parsed successfully. Found ${parsedMessages.length} messages.`);
    } catch (parseError) {
      console.error("Error parsing chat file:", parseError);
      return c.json({
        error: "Failed to parse chat file.",
        details: parseError instanceof Error ? parseError.message : String(parseError)
      }, 400);
    }

    // Get available participants
    const participants = await getAvailableParticipants(parsedMessages);
    
    return c.json({
      participants,
      totalMessages: parsedMessages.length
    });

  } catch (error) {
    console.error("Error in /participants endpoint:", error);
    return c.json({
      error: "Internal server error while processing participants",
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

// --- /submit Route (Modified for Localhost Testing) ---
// LOCALHOST TESTING: Authentication and quota checks are commented out for local development
app.post("/submit", async (c) => { // Reverted to POST /submit
  try {
    console.log("Received /submit request");

    // LOCALHOST TESTING: Comment out device ID validation
    // Extract device ID from Authorization header
    // const deviceId = extractDeviceId(c.req.raw);
    const deviceId = "localhost-test-device"; // Use dummy device ID for localhost
    
    // if (!deviceId) {
    //   console.error("No valid device ID provided in Authorization header");
    //   return c.json({
    //     error: "Valid device ID required in Authorization header (Bearer <uuid>)"
    //   }, 401);
    // }

    console.log(`Processing request for device: ${deviceId}`);

    // LOCALHOST TESTING: Comment out rate limiting
    // Check rate limiting
    // if (!checkRateLimit(deviceId)) {
    //   console.log(`Rate limit exceeded for device: ${deviceId}`);
    //   return c.json({
    //     error: "Rate limit exceeded. Please wait before making another request."
    //   }, 429);
    // }

    // Log headers for debugging
    try {
      const headers = Object.fromEntries(c.req.raw.headers);
      console.log("Request headers:", JSON.stringify(headers, null, 2));
    } catch (error) {
      console.error("Error logging headers:", error);
    }

    const formData = await c.req.formData();
    console.log("FormData keys:", [...formData.keys()]);

    // Check if this is a character analysis request
    const isCharacterAnalysis = formData.get("analysisType") === "character";

    // Get the file based on the analysis type
    const file = isCharacterAnalysis
      ? formData.get("userfile")
      : formData.get("chatfile");

    if (!file || !(file instanceof File)) {
      console.error("No valid file found in request");
      return c.json({
        error: isCharacterAnalysis ? "User file is required." : "Chat file is required."
      }, 400);
    }

    const fileName = file.name;
    console.log(`Received file: ${fileName}, size: ${file.size}`);

    // Read the file content
    const fileContent = await file.text();
    console.log(`File content (first 100 chars): ${fileContent.substring(0, 100)}...`);

    // If this is a character analysis request, handle it differently
    if (isCharacterAnalysis) {
      // LOCALHOST TESTING: Comment out quota check for character analysis
      // For character analysis, still check quota but with lower cost
      // const quotaCheck = await checkAndChargeQuota(deviceId, c.env, 0.08) as any;
      
      // if (!quotaCheck.allowed) {
      //   console.log(`Quota exceeded for character analysis: ${deviceId}`);
      //   return c.json({
      //     error: quotaCheck.message || "Analysis quota exceeded",
      //     quotaInfo: {
      //       tier: quotaCheck.tier,
      //       remaining: quotaCheck.remaining,
      //       resetDate: quotaCheck.resetDate
      //     }
      //   }, 429);
      // }

      const archetypeResult = await handleCharacterAnalysis(
        c.env.GEMINI_API_KEY,
        fileContent,
        fileName
      );

      if (!archetypeResult) {
        return c.json({
          error: "Failed to analyze user file and assign an archetype."
        }, 500);
      }

      // Return the archetype result
      return c.json(archetypeResult);
    }

    // Otherwise, continue with chat analysis
    // 1. Parse the chat file in memory (auto-detect format)
    let parsedMessages: ParsedMessage[];
    try {
      parsedMessages = parseChatFile(fileContent, fileName);
      
      if (parsedMessages.length === 0) {
        console.error("No messages parsed from file, returning a 500 error.");
        return c.json({
          error: "The chat file appears to be empty or in an unsupported format. Please check the file and try again."
        }, 500); // Use a 500-level error to distinguish from client errors.
      }
      console.log(`File parsed successfully. Found ${parsedMessages.length} messages.`);
    } catch (parseError) {
      console.error("Error parsing chat file:", parseError);
      return c.json({
        error: "Something went wrong while processing your chat file. Please ensure it is a valid WhatsApp (.txt) or Instagram (.json) export.",
        details: parseError instanceof Error ? parseError.message : String(parseError)
      }, 500); // Use a 500-level error for server-side parsing failures.
    }

    // Extract analysis type from formData
    let analysisType: string | undefined = undefined;
    const analysisTypeString = formData.get("analysisType") as string | null;
    if (analysisTypeString && analysisTypeString !== "character") {
      analysisType = analysisTypeString;
      console.log(`Analysis type specified: ${analysisType}`);
    } else {
      // Default to group for backward compatibility
      analysisType = "group";
      console.log("No analysis type specified, defaulting to 'group'");
    }

    // LOCALHOST TESTING: Comment out cost estimation and quota checks
    // PHASE 3 FIX: Check cache status for accurate cost estimation
    // Import getCacheStatus for cache-aware cost estimation
    // const { getCacheStatus } = await import('./analysis/llm');
    // const cacheStatus = getCacheStatus();
    // const estimatedCost = estimateAnalysisCost(parsedMessages.length, analysisType, cacheStatus.active);
    const estimatedCost = 0.10; // Dummy cost for localhost testing
    console.log(`Estimated cost for ${parsedMessages.length} messages (${analysisType}): $${estimatedCost} (LOCALHOST TESTING)`);

    // Check quota and charge usage BEFORE doing expensive LLM analysis
    // const quotaCheck = await checkAndChargeQuota(deviceId, c.env, estimatedCost) as any;
    
    // if (!quotaCheck.allowed) {
    //   console.log(`Quota exceeded for device ${deviceId}: ${quotaCheck.message}`);
    //   return c.json({
    //     error: quotaCheck.message || "Analysis quota exceeded",
    //     quotaInfo: {
    //       tier: quotaCheck.tier,
    //       remaining: quotaCheck.remaining,
    //       resetDate: quotaCheck.resetDate
    //     }
    //   }, 429);
    // }

    console.log(`Quota check passed for device ${deviceId} (LOCALHOST TESTING)`);

    // --- Perform Analysis Synchronously (for local testing) ---
    const analysisId = nanoid(); // Generate unique ID here for sync mode
    console.log(`Starting synchronous analysis for ID: ${analysisId}`);

    // Extract selected LLM tasks from formData
    let selectedLlmTaskIds: string[] | undefined = undefined;
    const selectedLlmTaskIdsString = formData.get("selectedLlmTaskIds") as string | null;
    if (selectedLlmTaskIdsString) {
      try {
        selectedLlmTaskIds = JSON.parse(selectedLlmTaskIdsString);
        if (!Array.isArray(selectedLlmTaskIds) || !selectedLlmTaskIds.every(task => typeof task === 'string')) {
          console.warn("selectedLlmTaskIds is not an array of strings, ignoring.");
          selectedLlmTaskIds = undefined;
        } else {
          console.log("Selected LLM Task IDs:", selectedLlmTaskIds);
        }
      } catch (e) {
        console.warn("Failed to parse selectedLlmTaskIds, proceeding with default behavior:", e);
        selectedLlmTaskIds = undefined;
      }
    }

    // Extract selected participants from formData (for love analysis)
    let selectedParticipants: string[] | undefined = undefined;
    const selectedParticipantsString = formData.get("selectedParticipants") as string | null;
    if (selectedParticipantsString && analysisType === 'love') {
      try {
        selectedParticipants = JSON.parse(selectedParticipantsString);
        if (!Array.isArray(selectedParticipants) || !selectedParticipants.every(participant => typeof participant === 'string')) {
          console.warn("selectedParticipants is not an array of strings, ignoring.");
          selectedParticipants = undefined;
        } else {
          console.log("Selected Participants for love analysis:", selectedParticipants);
        }
      } catch (e) {
        console.warn("Failed to parse selectedParticipants, proceeding with default behavior:", e);
        selectedParticipants = undefined;
      }
    }

    try {
      // Pass the environment bindings (including GEMINI_API_KEY)
      // Use retry logic for the full analysis
      const analysisResult = await retry(
        async () => performFullAnalysis(parsedMessages, fileName, analysisId, c.env, selectedLlmTaskIds, analysisType, selectedParticipants),
        2, // Max 2 retries
        1000, // 1 second initial delay
        (error, attempt) => {
          console.log(`Retry attempt ${attempt} for analysis after error: ${error.message}`);
        }
      );

      console.log(`Synchronous analysis complete for ID: ${analysisId}`);

      // Ensure the result has all the expected sections
      // Concise logging of analysis result state
      console.log("Analysis result BEFORE completion - participants:",
                  `${analysisResult.participants?.length || 0} participants,`,
                  `${analysisResult.personStats?.length || 0} personStats`);

      // Log participant names to verify all are present
      if (analysisResult.participants) {
        console.log("Participant names:", analysisResult.participants.map((p: {name: string}) => p.name).join(", "));
      }

      // Log personStats in detail to debug the issue
      if (analysisResult.personStats) {
        console.log("personStats details:");
        analysisResult.personStats.forEach((stat: any, index: number) => {
          console.log(`[${index}] name: ${stat.name}, archetype: ${stat.archetype}`);
        });
      }

      // Use retry logic for ensuring complete analysis result
      const completeResult = await retry(
        async () => ensureCompleteAnalysisResult(analysisResult),
        2, // Max 2 retries
        500, // 500ms initial delay
        (error, attempt) => {
          console.log(`Retry attempt ${attempt} for ensuring complete analysis result after error: ${error.message}`);
        }
      );

      // PHASE 5 FIX: Enhanced token usage reporting with cache efficiency
      if (analysisResult.tokenUsage) {
        // Include cache creation tokens in input tokens (as that's how it's actually calculated)
        const cacheCreationTokens = analysisResult.tokenUsage.totalCachedTokens || 0;
        const actualInputTokens = (analysisResult.tokenUsage.totalInputTokens || 0) + cacheCreationTokens;
        const cacheCreationCost = (cacheCreationTokens / 1000000) * 0.1;
        const totalActualInputCost = (analysisResult.tokenUsage.inputCost || 0) + cacheCreationCost;
        
        console.log("\n===== TOKEN USAGE SUMMARY =====");
        console.log(`Total Input Tokens: ${actualInputTokens.toLocaleString()} (${analysisResult.tokenUsage.totalInputTokens?.toLocaleString() || '0'} new + ${cacheCreationTokens.toLocaleString()} cache creation)`);
        console.log(`Total Output Tokens: ${analysisResult.tokenUsage.totalOutputTokens.toLocaleString()}`);
        console.log(`Total Cached Tokens: ${analysisResult.tokenUsage.totalCachedTokens?.toLocaleString() || '0'}`);
        
        console.log(`\n--- COST BREAKDOWN ---`);
        console.log(`Input Cost: $${totalActualInputCost.toFixed(6)} (${(analysisResult.tokenUsage.inputCost || 0).toFixed(6)} new + ${cacheCreationCost.toFixed(6)} cache creation)`);
        console.log(`Output Cost: $${analysisResult.tokenUsage.outputCost?.toFixed(6) || '0.000000'}`);
        console.log(`Cache Access Cost: $${analysisResult.tokenUsage.cachingCost?.toFixed(6) || '0.000000'}`);
        console.log(`Storage Cost: $${analysisResult.tokenUsage.storageCost?.toFixed(6) || '0.000000'} (actual duration)`);
        console.log(`Total Cost: $${(analysisResult.tokenUsage.totalCost + cacheCreationCost).toFixed(6)}`);
        
        // Show cache usage stats if applicable
        if (analysisResult.tokenUsage.totalCachedTokens > 0) {
          console.log(`\n--- CACHE USAGE ---`);
          console.log(`Cached tokens accessed: ${analysisResult.tokenUsage.totalCachedTokens.toLocaleString()}`);
          console.log(`New input tokens: ${analysisResult.tokenUsage.totalInputTokens.toLocaleString()}`);
        }
        console.log("================================\n");

        // LOCALHOST TESTING: Comment out cost data storage
        // Store cost data for tracking (non-blocking)
        console.log('🎯 Cost tracking disabled for localhost testing');
        // try {
        //   const totalCostWithCache = analysisResult.tokenUsage.totalCost + cacheCreationCost;
        //   console.log(`🎯 Cost to store: $${totalCostWithCache.toFixed(6)}`);
        //   console.log('🎯 COST_TRACKER available:', !!c.env.COST_TRACKER);
          
        //   // Temporarily await to see the actual error
        //   await storeCostData(deviceId, totalCostWithCache, analysisResult.tokenUsage, c.env);
        //   console.log('🎯 Cost tracking initiated successfully');
        // } catch (costError) {
        //   // Don't fail the analysis if cost tracking fails
        //   console.log('❌ Cost tracking failed (non-critical):', costError);
        // }
      }

      // 5. Return the full analysis result directly
      console.log("Final analysisResult structure:", Object.keys(completeResult));

      // Final verification
      console.log(`Final verification - personStats present: ${!!completeResult.personStats}, count: ${completeResult.personStats?.length || 0}`);

      // LOCALHOST TESTING: Comment out quota info in response
      // Include quota information in successful responses
      // const quotaInfo = {
      //   creditsUsed: estimatedCost,
      //   remaining: quotaCheck.remaining,
      //   tier: quotaCheck.tier,
      //   resetDate: quotaCheck.resetDate
      // };
      
      return c.json({
        ...completeResult
        // quotaInfo  // Commented out for localhost testing
      }); // Return JSON result
    } catch (analysisError) {
      console.error("Error during analysis after retries:", analysisError);

      // Clean up the cache even if there's an error
      if (c.env.GEMINI_API_KEY) {
        console.log("Cleaning up chat cache after error...");
        try {
          const cacheDeleted = await handleCacheDelete(c.env.GEMINI_API_KEY);
          console.log(`Cache deletion ${cacheDeleted ? 'successful' : 'failed or not needed'}`);
        } catch (cacheError) {
          console.error("Error deleting cache after analysis error:", cacheError);
        }
      }

      // Return error message without mock data
      return c.json({
        error: "Analysis failed after multiple retry attempts",
        details: analysisError instanceof Error ? analysisError.message : String(analysisError)
      }, 500);
    }
  } catch (unexpectedError) {
    console.error("Unexpected error in /submit handler:", unexpectedError);

    // Clean up the cache even if there's an unexpected error
    if (c.env.GEMINI_API_KEY) {
      console.log("Cleaning up chat cache after unexpected error...");
      try {
        const cacheDeleted = await handleCacheDelete(c.env.GEMINI_API_KEY);
        console.log(`Cache deletion ${cacheDeleted ? 'successful' : 'failed or not needed'}`);
      } catch (cacheError) {
        console.error("Error deleting cache after unexpected error:", cacheError);
      }
    }

    // Return error message without mock data
    return c.json({
      error: "Unexpected error occurred",
      details: unexpectedError instanceof Error ? unexpectedError.message : String(unexpectedError)
    }, 500);
  }
});

// Handle RevenueCat webhooks
app.post("/feedback", async (c) => {
  try {
    const { userId, feedbackType, email, message } = await c.req.json();

    // Basic validation
    if (!userId || !message || !feedbackType) {
      return c.json({ error: "Missing required feedback fields (userId, message, feedbackType)" }, 400);
    }

    // Log the feedback data
    // Cloudflare will automatically capture console.log output
    console.log('Feedback Received:', JSON.stringify({
      userId,
      type: feedbackType,
      email: email || null, // Ensure email is explicitly null if not provided
      message,
      timestamp: new Date().toISOString()
    }));

    // Check for special feedback message to grant free credits
    if (message.trim() === "give-free-credits-right-now") {
      console.log(`Special feedback detected: granting 10 credits to user ${userId}`);
      
      try {
        // Import CreditService for direct credit granting
        const { CreditService } = await import('./services/credit');
        
        // Generate a unique transaction ID for this feedback-based grant
        const feedbackTransactionId = `FEEDBACK_${userId}_${Date.now()}`;
        
        // Grant credits directly using CreditService
        await CreditService.grantCredits(feedbackTransactionId, 10, c.env);
        
        // Associate the user with this transaction
        await CreditService.associateUserWithTransaction(userId, feedbackTransactionId, c.env);
        
        console.log(`Successfully granted 10 credits to ${userId} via transaction ${feedbackTransactionId}`);
        
        return c.json({
          success: true,
          message: "Feedback received and 10 credits granted!",
          creditsGranted: true,
          transactionId: feedbackTransactionId,
          creditsAmount: 10
        }, 200);
        
      } catch (creditError) {
        console.error(`Error attempting to grant credits to ${userId}:`, creditError);
        return c.json({
          success: true,
          message: "Feedback received, but credit granting encountered an error",
          creditsGranted: false,
          error: creditError instanceof Error ? creditError.message : String(creditError)
        }, 200);
      }
    }

    return c.json({ success: true, message: "Feedback received" }, 200);
  } catch (error) {
    console.error("Error processing feedback:", error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    return c.json({ error: "Failed to process feedback", details: errorMessage }, 500);
  }
});
app.post("/revenuecat-webhook", async (c) => { // Changed path to match RevenueCat config
  console.log('[RevenueCat] Webhook received at /revenuecat-webhook'); // Updated log message
  try {
    const rawBody = await c.req.text(); // Still need the body to parse the event
    const authorizationHeader = c.req.header('Authorization');

    if (!c.env.REVENUECAT_AUTH_TOKEN) {
      console.error("[RevenueCat] Authorization token (REVENUECAT_AUTH_TOKEN) not configured in environment.");
      return c.json({ error: "Webhook authorization token not configured." }, 500);
    }

    // The expected token usually includes "Bearer ", so we might need to check for that
    // Or, if RevenueCat sends the raw token directly in the "Authorization header value" field,
    // then c.env.REVENUECAT_AUTH_TOKEN should be that raw token.
    // For now, let's assume RevenueCat sends the exact value you typed into the "Authorization header value" field.
    // If RevenueCat prepends "Bearer ", you'd adjust the check or your env var.
    // A common practice is for the "Authorization header value" to be just the token,
    // and RevenueCat might send it as "Authorization: <your_token_here>" or "Authorization: Bearer <your_token_here>"
    // Let's assume for now it's a direct match to what you put in the field.
    
    const expectedAuthToken = c.env.REVENUECAT_AUTH_TOKEN;
    let providedToken = authorizationHeader;

    // If RevenueCat sends "Bearer <token>", extract the token part.
    // Otherwise, if it sends just "<token>", this check is fine.
    const bearerPrefix = "Bearer ";
    if (providedToken && providedToken.startsWith(bearerPrefix)) {
        providedToken = providedToken.substring(bearerPrefix.length);
    }
    // If RevenueCat sends the token directly without "Bearer ", and you've stored it raw,
    // then the above extraction is not strictly needed but doesn't hurt if `providedToken` doesn't start with "Bearer ".
    // However, if RevenueCat sends "Authorization: <your_token>" and you stored "<your_token>" in env,
    // and the header comes as "Authorization: Bearer <your_token>", then the comparison will fail.
    // For maximum flexibility, let's assume the env var STORES THE RAW TOKEN.
    // And we check if the incoming header IS the raw token OR "Bearer " + raw token.

    let isValid = false;
    if (providedToken === expectedAuthToken) {
        isValid = true;
    } else if (authorizationHeader === `${bearerPrefix}${expectedAuthToken}`) {
        // This covers the case where RC sends "Bearer <token>" and you stored "<token>"
        isValid = true;
    }


    if (!isValid) {
      console.warn(`[RevenueCat] Invalid Authorization header. Received: "${authorizationHeader}", Expected: "${expectedAuthToken}" (or Bearer prefixed)`);
      return c.json({ error: 'Invalid authorization' }, 401); // 401 for unauthorized
    }

    console.log('[RevenueCat] Webhook authorization VERIFIED');
    const webhookPayload = JSON.parse(rawBody) as { event: RevenueCatWebhookEvent }; // RevenueCat wraps the event

    if (!webhookPayload || !webhookPayload.event) {
        console.error('[RevenueCat] Invalid webhook payload structure. Missing "event" object.');
        return c.json({ error: 'Invalid payload structure' }, 400);
    }

    // We still need the revenueCatClient to process the event, but not for verification
    const revenueCatClient = createRevenueCatAPIClient(c.env);
    await revenueCatClient.processWebhookEvent(webhookPayload.event);

    return c.json({ success: true, message: `Processed ${webhookPayload.event.type} for user ${webhookPayload.event.app_user_id}` });

  } catch (error) {
    console.error('❌ RevenueCat webhook error:', error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    // Avoid logging the entire error object if it might contain sensitive data from the request
    if (error instanceof Error && error.stack) {
      console.error(error.stack);
    }
    return c.json({ error: "Webhook processing failed", details: errorMessage }, 500);
  }
});

// Sync subscription status (now uses SubscriptionService for RevenueCat)
app.post("/sync-subscription", async (c) => {
  const deviceId = extractDeviceId(c.req.raw);
  
  if (!deviceId) {
    return c.json({ error: "Valid device ID required in Authorization header" }, 401);
  }
  
  try {
    console.log(`[SyncSubscription] Syncing subscription status for device: ${deviceId} using SubscriptionService`);
    
    const subDetails = await SubscriptionService.getSubscriptionDetails(deviceId, c.env);
    
    // The SubscriptionDetails interface matches well with what the client might expect.
    // We can adapt the response structure if needed.
    if (subDetails.hasActiveEntitlement) {
      console.log(`[SyncSubscription] Active subscription found for ${deviceId}:`, subDetails);
      return c.json({
        success: true,
        message: "Subscription synced successfully.",
        subscription_status: { // Mirroring a common structure
            has_active_subscription: subDetails.hasActiveEntitlement,
            analyses_remaining: subDetails.analyses_remaining, // Now correctly using analyses_remaining
            expires_at: subDetails.expiresAt,
            period_type: subDetails.periodType,
            days_remaining: subDetails.daysRemaining,
            is_trial: subDetails.periodType === 'trial',
            // subscription_id: subDetails.productId, // Or a more specific ID if available in SubscriptionDetails
        },
        details: subDetails, // Return the full details object
        is_sandbox: subDetails.isSandbox
      });
    } else {
      console.log(`[SyncSubscription] No active subscription found for ${deviceId}`);
      return c.json({
        success: true,
        message: "No active subscription found.",
        subscription_status: null,
        details: subDetails,
        is_sandbox: subDetails.isSandbox
      });
    }
  } catch (error) {
    console.error("[SyncSubscription] Error syncing subscription:", error);
    return c.json({
      success: false,
      error: "Failed to sync subscription status",
      details: error instanceof Error ? error.message : 'Unknown error'
    }, 500);
  }
});

// Restore purchases endpoint (now uses SubscriptionService for RevenueCat)
// The actual "restore" happens client-side with RevenueCat SDK.
// This endpoint just fetches the latest status from our backend.
app.post("/restore-purchases", async (c) => {
  const deviceId = extractDeviceId(c.req.raw);
  
  if (!deviceId) {
    return c.json({ error: "Valid device ID required in Authorization header" }, 401);
  }
  
  try {
    console.log(`[RestorePurchases] Restoring purchases for device: ${deviceId} by fetching latest status`);
    
    const subDetails = await SubscriptionService.getSubscriptionDetails(deviceId, c.env);
    
    if (subDetails.hasActiveEntitlement) {
      return c.json({
        message: "Active subscription status retrieved successfully.",
        restored: true, // Indicates status was checked and active sub found
        subscription_status: { // Mirroring a common structure
            has_active_subscription: subDetails.hasActiveEntitlement,
            analyses_remaining: subDetails.analyses_remaining, // Now correctly using analyses_remaining
            expires_at: subDetails.expiresAt,
            period_type: subDetails.periodType,
            days_remaining: subDetails.daysRemaining,
            is_trial: subDetails.periodType === 'trial',
        },
        details: subDetails,
        is_sandbox: subDetails.isSandbox
      });
    } else {
      return c.json({
        message: "No active subscription found to restore.",
        restored: false,
        subscription_status: null,
        details: subDetails,
        is_sandbox: subDetails.isSandbox
      });
    }
    
  } catch (error) {
    console.error("[RestorePurchases] Error restoring purchases:", error);
    return c.json({
      error: "Failed to restore purchases",
      details: error instanceof Error ? error.message : 'Unknown error',
      restored: false
    }, 500);
  }
});

// Transaction sync endpoint removed - Qonversion specific, RevenueCat uses webhooks.

// --- Debug Endpoints ---
// app.get("/debug/qonversion/:deviceId", async (c) => { // Removed
// ... Qonversion debug endpoint removed ...
// });

app.get("/debug/subscription/:deviceId", async (c) => {
  const deviceId = c.req.param('deviceId');
  if (!deviceId) {
    return c.json({ error: "Device ID parameter is required" }, 400);
  }
  try {
    const details = await SubscriptionService.getSubscriptionDetails(deviceId, c.env);
    // const customerInfo = await SubscriptionService.getCustomerInfo(deviceId, c.env); // Qonversion specific, removed.
    return c.json({
      message: `Subscription details for ${deviceId}`,
      subscriptionDetails: details,
      // rawRevenueCatCustomerInfo: customerInfo, // Qonversion specific, removed.
    });
  } catch (error) {
    console.error(`Error fetching debug subscription info for ${deviceId}:`, error);
    return c.json({ error: "Failed to fetch subscription details", details: error instanceof Error ? error.message : String(error) }, 500);
  }
});

app.get("/debug/credits/:deviceId", async (c) => {
  const deviceId = c.req.param('deviceId');
  if (!deviceId) {
    return c.json({ error: "Device ID parameter is required" }, 400);
  }
  try {
    // Get all transaction IDs associated with this device from TransactionCreditsStore
    const tcStore = c.env.TRANSACTION_CREDITS.get(c.env.TRANSACTION_CREDITS.idFromName("global"));
    let transactionIds: string[] = [];
    const userTxResponse = await tcStore.fetch(
      new Request(INTERNAL_ENDPOINTS.TRANSACTION_CREDITS.GET_USER_TRANSACTIONS, {
        method: "GET", // Method is GET as per TransactionCreditsStore
        headers: { "Content-Type": "application/json" }, // GET usually doesn't need Content-Type for body, but let's be explicit if DO expects it
        body: JSON.stringify({ user_id: deviceId }), // Send user_id in body as per DO
      })
    );

    if (userTxResponse.ok) {
      const userTxData = await userTxResponse.json() as { transaction_ids?: string[] };
      transactionIds = userTxData.transaction_ids || [];
      console.log(`[Debug /credits] Fetched ${transactionIds.length} transaction IDs for user ${deviceId} from TransactionCreditsStore.`);
    } else {
      console.error(`[Debug /credits] Failed to get user transaction IDs for ${deviceId} from TransactionCreditsStore: ${userTxResponse.status}`);
      // Proceed with empty transactionIds, or return error depending on desired behavior
    }
    
    let totalSubscriptionCredits = 0;
    const transactionCreditDetails: Record<string, number> = {};

    if (transactionIds.length > 0) {
      const store = c.env.TRANSACTION_CREDITS.get(
        c.env.TRANSACTION_CREDITS.idFromName("global")
      );
      const creditsPromises = transactionIds.map(async (txId) => {
        const response = await store.fetch(
          new Request(INTERNAL_ENDPOINTS.TRANSACTION_CREDITS.GET_CREDITS, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ transaction_id: txId }),
          })
        );
        if (response.ok) {
          const data = await response.json() as { credits?: number };
          return { txId, credits: data.credits || 0 };
        }
        console.error(`Failed to get credits for txId ${txId} during debug: ${response.status}`);
        return { txId, credits: 0 };
      });
      const creditsResults = await Promise.all(creditsPromises);
      
      creditsResults.forEach(res => {
        transactionCreditDetails[res.txId] = res.credits;
        totalSubscriptionCredits += res.credits;
      });
    }
    
    // Also get free tier quota info from DeviceQuota DO
    const deviceQuotaId = c.env.DEVICE_QUOTA.idFromName(deviceId);
    const deviceQuotaStub = c.env.DEVICE_QUOTA.get(deviceQuotaId);
    const quotaResponse = await deviceQuotaStub.fetch(new Request('http://internal/quota', { method: 'GET' }));
    let freeTierInfo: any = { message: "Could not fetch free tier info." };
    if (quotaResponse.ok) {
        freeTierInfo = await quotaResponse.json();
    }

    return c.json({
      message: `Credit details for ${deviceId}`,
      associatedTransactionIdsFromRC: [...new Set(transactionIds)],
      transactionCreditDetailsFromStore: transactionCreditDetails,
      totalSubscriptionCreditsInStore: totalSubscriptionCredits,
      freeTierInfo: freeTierInfo,
    });
  } catch (error) {
    console.error(`Error fetching debug credit info for ${deviceId}:`, error);
    return c.json({ error: "Failed to fetch credit details", details: error instanceof Error ? error.message : String(error) }, 500);
  }
});

// --- New Debug Endpoints for Simplified System ---
app.get("/debug/credits-v2/:deviceId", async (c) => {
  const deviceId = c.req.param('deviceId');
  if (!deviceId) {
    return c.json({ error: "Device ID parameter is required" }, 400);
  }
  
  try {
    // Get user's total credits and transaction IDs from TransactionCreditsStore
    const tcStore = c.env.TRANSACTION_CREDITS.get(c.env.TRANSACTION_CREDITS.idFromName("global"));
    
    let userTotalCredits = 0;
    let userTransactionIds: string[] = [];
    let errorFetchingCredits: string | null = null;

    // Fetch total credits
    const totalCreditsResponse = await tcStore.fetch(
      new Request(INTERNAL_ENDPOINTS.TRANSACTION_CREDITS.GET_USER_TOTAL_CREDITS, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: deviceId }),
      })
    );
    if (totalCreditsResponse.ok) {
      const data = await totalCreditsResponse.json() as { total_credits?: number };
      userTotalCredits = data.total_credits || 0;
    } else {
      errorFetchingCredits = `Failed to fetch total credits: ${totalCreditsResponse.status}`;
      console.error(`[Debug /credits-v2] ${errorFetchingCredits} for user ${deviceId}`);
    }

    // Fetch transaction IDs
    const userTxResponse = await tcStore.fetch(
      new Request(INTERNAL_ENDPOINTS.TRANSACTION_CREDITS.GET_USER_TRANSACTIONS, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ user_id: deviceId }),
      })
    );
    if (userTxResponse.ok) {
      const data = await userTxResponse.json() as { transaction_ids?: string[] };
      userTransactionIds = data.transaction_ids || [];
    } else {
      const txError = `Failed to fetch transaction IDs: ${userTxResponse.status}`;
      errorFetchingCredits = errorFetchingCredits ? `${errorFetchingCredits}; ${txError}` : txError;
      console.error(`[Debug /credits-v2] ${txError} for user ${deviceId}`);
    }

    const creditDetails = errorFetchingCredits
      ? { error: errorFetchingCredits }
      : { total_credits: userTotalCredits, transaction_ids: userTransactionIds };
    
    // Get device info from DeviceQuota
    const deviceQuota = c.env.DEVICE_QUOTA.get(c.env.DEVICE_QUOTA.idFromName(deviceId));
    const deviceInfoResponse = await deviceQuota.fetch(
      new Request(INTERNAL_ENDPOINTS.DEVICE_QUOTA.INFO, { method: 'GET' })
    );
    
    let deviceInfo = { error: "Failed to fetch device info" };
    if (deviceInfoResponse.ok) {
      deviceInfo = await deviceInfoResponse.json();
    }
    
    return c.json({
      message: `Credit details v2 for ${deviceId}`,
      creditDetailsFromStore: creditDetails,
      deviceInfoFromQuotaDO: deviceInfo,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error(`Error fetching debug credit info v2 for ${deviceId}:`, error);
    return c.json({
      error: "Failed to fetch credit details v2",
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

app.post("/debug/grant-test-credits/:deviceId", async (c) => {
  const deviceId = c.req.param('deviceId');
  if (!deviceId) {
    return c.json({ error: "Device ID parameter is required" }, 400);
  }
  
  try {
    const body = await c.req.json() as {
      product_id?: string;
      credits?: number;
      expires_in_days?: number;
    };
    
    const productId = body.product_id || "weekly_sub_20_credits";
    const credits = body.credits || 20;
    const expiresInDays = body.expires_in_days || 7;
    
    const now = Date.now();
    const expiresAt = now + (expiresInDays * 24 * 60 * 60 * 1000);
    const transactionId = `TEST_${deviceId}_${now}`;
    
    // Grant credits via TransactionCreditsStore
    const tcStore = c.env.TRANSACTION_CREDITS.get(c.env.TRANSACTION_CREDITS.idFromName("global"));
    const grantResponse = await tcStore.fetch(
      new Request('http://internal/grant', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          transaction_id: transactionId, // Corrected key
          credits: credits // Corrected key
        })
      })
    );

    let grantResult: any = { success: false };
    let associationResult: any = { success: false, message: "Association not attempted or grant failed." };

    if (grantResponse.ok) {
      grantResult = await grantResponse.json();
      if (grantResult.success) {
        console.log(`Debug credits granted for ${deviceId}, transaction ${transactionId}. Attempting association.`);
        // Associate user with transaction
        const associateResponse = await tcStore.fetch(
          new Request(INTERNAL_ENDPOINTS.TRANSACTION_CREDITS.ASSOCIATE_USER, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              user_id: deviceId,
              transaction_id: transactionId
            })
          })
        );
        if (associateResponse.ok) {
          associationResult = await associateResponse.json();
          console.log(`Successfully associated user ${deviceId} with transaction ${transactionId} in debug grant.`);
        } else {
          const errorText = await associateResponse.text();
          associationResult = { success: false, message: `Association failed: ${associateResponse.status} ${errorText}` };
          console.error(`Failed to associate user ${deviceId} with transaction ${transactionId} in debug grant: ${associateResponse.status} ${errorText}`);
        }
      } else {
        console.error(`Debug credit grant reported failure by DO for ${deviceId}, transaction ${transactionId}:`, grantResult);
      }
    } else {
      const errorText = await grantResponse.text();
      grantResult = { success: false, message: `Grant request failed: ${grantResponse.status} ${errorText}` };
      console.error(`Debug credit grant HTTP request failed for ${deviceId}, transaction ${transactionId}: ${grantResponse.status} ${errorText}`);
    }
    
    // Update device tier
    const deviceQuota = c.env.DEVICE_QUOTA.get(c.env.DEVICE_QUOTA.idFromName(deviceId));
    await deviceQuota.fetch(
      new Request('http://internal/force-tier-update', { method: 'POST' })
    );
    
    return c.json({
      message: grantResult.success && associationResult.success ? "Test credits granted and associated successfully" : "Test credit operation encountered issues.",
      transactionId,
      grantDetails: grantResult,
      associationDetails: associationResult,
      productId, // Kept for informational response
      creditsGranted: credits, // Kept for informational response
      expiresAt: new Date(expiresAt).toISOString() // Kept for informational response
    });
  } catch (error) {
    console.error(`Error in /debug/grant-test-credits for ${deviceId}:`, error);
    return c.json({
      error: "Failed to grant test credits",
      details: error instanceof Error ? error.message : String(error)
    }, 500);
  }
});

// --- Cost Tracking Endpoints ---

// Get total cost (HTML page)
app.get("/costs", async (c) => {
  try {
    const costData = await getCostData(c.env, 20) as {
      totalCost: number;
      recentCosts: Array<{
        totalCost: number;
        timestamp: string | number;
        tokenUsage?: {
          totalInputTokens?: number;
          totalOutputTokens?: number;
          totalCachedTokens?: number;
          inputCost?: number;
          outputCost?: number;
          cachingCost?: number;
        };
      }>;
      lastUpdated: string;
      error?: string;
    };
    
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>API Cost Tracker</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .total-cost { background: #e1f5fe; padding: 20px; border-radius: 8px; margin-bottom: 20px; text-align: center; }
        .total-cost h1 { color: #01579b; margin: 0; font-size: 2em; }
        .total-cost p { color: #0277bd; margin: 5px 0; font-size: 1.2em; }
        .recent-costs { margin-top: 20px; }
        .cost-entry { background: #fafafa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #2196f3; }
        .cost-entry h3 { margin: 0 0 10px 0; color: #333; }
        .cost-entry p { margin: 5px 0; color: #666; }
        .cost-breakdown { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-top: 10px; }
        .cost-item { background: #f0f0f0; padding: 8px; border-radius: 3px; }
        .timestamp { font-size: 0.9em; color: #888; }
        .refresh-btn { background: #2196f3; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin-bottom: 20px; }
        .refresh-btn:hover { background: #1976d2; }
    </style>
</head>
<body>
    <div class="container">
        <div class="total-cost">
            <h1>Total API Cost</h1>
            <p>$${costData.totalCost.toFixed(6)}</p>
            <p>Updated: ${costData.lastUpdated}</p>
            ${costData.error ? `<p style="color: #f44336; font-size: 0.9em;">Status: ${costData.error}</p>` : ''}
        </div>
        
        <button class="refresh-btn" onclick="window.location.reload()">Refresh</button>
        
        <div class="recent-costs">
            <h2>Recent Analysis Costs (Last 20)</h2>
            ${costData.recentCosts.map(cost => `
                <div class="cost-entry">
                    <h3>Analysis Entry</h3>
                    <p><strong>Cost: $${cost.totalCost.toFixed(6)}</strong></p>
                    <p class="timestamp">${new Date(cost.timestamp).toLocaleString()}</p>
                    ${cost.tokenUsage ? `
                        <div class="cost-breakdown">
                            <div class="cost-item">Input Tokens: ${cost.tokenUsage.totalInputTokens?.toLocaleString() || 0}</div>
                            <div class="cost-item">Output Tokens: ${cost.tokenUsage.totalOutputTokens?.toLocaleString() || 0}</div>
                            <div class="cost-item">Cached Tokens: ${cost.tokenUsage.totalCachedTokens?.toLocaleString() || 0}</div>
                            <div class="cost-item">Input Cost: $${cost.tokenUsage.inputCost?.toFixed(6) || '0.000000'}</div>
                            <div class="cost-item">Output Cost: $${cost.tokenUsage.outputCost?.toFixed(6) || '0.000000'}</div>
                            <div class="cost-item">Cache Cost: $${cost.tokenUsage.cachingCost?.toFixed(6) || '0.000000'}</div>
                        </div>
                    ` : ''}
                </div>
            `).join('')}
        </div>
    </div>
</body>
</html>`;
    
    return c.html(html);
  } catch (error) {
    console.error('Error fetching cost data:', error);
    return c.text('Error fetching cost data', 500);
  }
});

// Get cost data as JSON API
app.get("/api/costs", async (c) => {
  try {
    const costData = await getCostData(c.env, 50);
    return c.json(costData as Record<string, unknown>);
  } catch (error) {
    console.error('Error fetching cost data:', error);
    return c.json({ error: 'Failed to fetch cost data' }, 500);
  }
});

// Helper function to get cost data from CostTracker
async function getCostData(env: CloudflareBindings, limit: number = 20) {
  try {
    if (!env || !env.COST_TRACKER) {
      return {
        totalCost: 0,
        recentCosts: [],
        lastUpdated: new Date().toISOString(),
        error: 'Cost tracker not available'
      };
    }
    
    const costTrackerId = env.COST_TRACKER.idFromName('global');
    const costTracker = env.COST_TRACKER.get(costTrackerId);
    
    const response = await costTracker.fetch(new Request(`http://internal/get-costs?limit=${limit}`, {
      method: 'GET'
    }));
    
    if (response.ok) {
      return await response.json();
    } else {
      console.error('Failed to fetch cost data:', response.status);
      return {
        totalCost: 0,
        recentCosts: [],
        lastUpdated: new Date().toISOString(),
        error: `Failed to fetch cost data: ${response.status}`
      };
    }
  } catch (error) {
    console.error('Error fetching cost data:', error);
    return {
      totalCost: 0,
      recentCosts: [],
      lastUpdated: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Add explicit OPTIONS handler for CORS preflight requests
app.options("*", (_c) => {
  return new Response(null, {
    status: 204,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, PUT, DELETE, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type, Authorization, Accept, X-Requested-With",
      "Access-Control-Max-Age": "86400",
      "Access-Control-Allow-Credentials": "false",
    },
  });
});

// --- Export Fetch Handler and Durable Objects ---
export default {
  fetch: app.fetch, // Handles HTTP requests via Hono
  // queue: queueHandler, // Comment out queue handler for sync testing
};

// Export Durable Objects
export { DeviceQuota, TransactionCreditsStore, SubscriptionStore, CostTracker };
