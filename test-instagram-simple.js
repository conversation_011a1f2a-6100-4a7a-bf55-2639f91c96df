const fs = require('fs');

// Instagram system message patterns - these should keep the original sender
const INSTAGRAM_KEEP_SENDER_PATTERNS = [
  "sent an attachment", "liked a message", "reacted to", "started a video chat", 
  "missed a video chat", "started a call", "missed a call"
];

// Instagram system message patterns - these should change sender to "System"
const INSTAGRAM_SYSTEM_PATTERNS = [
  "you sent an attachment", "you liked a message", "you reacted", "you missed a video chat",
  "unsent a message", "changed the group photo", "added", "left the conversation", 
  "removed", "changed the group name"
];

// Check if message is a system message
function isInstagramSystemMessage(content) {
  const normalizedContent = content.toLowerCase().trim();
  
  // First check if it should keep sender (attachment, like, call, audio by others)
  const shouldKeepSender = INSTAGRAM_KEEP_SENDER_PATTERNS.some(pattern => 
    normalizedContent.includes(pattern.toLowerCase())
  );
  
  if (should<PERSON><PERSON><PERSON><PERSON>) {
    return true; // It's a system message but should keep sender
  }
  
  // Check if it's a system message that should change sender to "System"
  return INSTAGRAM_SYSTEM_PATTERNS.some(pattern => 
    normalizedContent.includes(pattern.toLowerCase())
  );
}

// Check if message should change sender to "System"
function shouldChangeToSystemSender(content) {
  const normalizedContent = content.toLowerCase().trim();
  return INSTAGRAM_SYSTEM_PATTERNS.some(pattern => 
    normalizedContent.includes(pattern.toLowerCase())
  );
}

// Updated parseInstagramJSON function with system message detection
function parseInstagramJSON(jsonContent) {
  try {
    const data = JSON.parse(jsonContent);
    
    return data.messages
      .filter(msg => msg.content && msg.content.trim() !== '')
      .map(msg => {
        const content = msg.content || '';
        const isSystemMsg = isInstagramSystemMessage(content);
        const shouldChangeToSystem = shouldChangeToSystemSender(content);
        
        return {
          timestamp: new Date(msg.timestamp_ms),
          sender: shouldChangeToSystem ? 'System' : msg.sender_name,
          text: content,
          isSystemMessage: isSystemMsg,
        };
      })
      .sort((a, b) => (a.timestamp?.getTime() ?? 0) - (b.timestamp?.getTime() ?? 0));
  } catch (error) {
    throw new Error('Invalid Instagram JSON format');
  }
}

console.log('=== First 10 messages from message_1 2.json ===');

// Test with message_1 2.json
try {
  const json1 = fs.readFileSync('message_1 2.json', 'utf8');
  const result1 = parseInstagramJSON(json1);
  
  result1.slice(0, 10).forEach((msg, i) => {
    const prefix = msg.isSystemMessage ? '📱 [SYSTEM]' : `${msg.sender}:`;
    console.log(`${i+1}. [${msg.timestamp.toLocaleString()}] ${prefix}`);
    console.log(`   ${msg.text}`);
    console.log();
  });
} catch (error) {
  console.error('Error testing message_1 2.json:', error.message);
}

console.log('=== First 10 messages from message_1 3.json ===');

// Test with message_1 3.json  
try {
  const json2 = fs.readFileSync('message_1 3.json', 'utf8');
  const result2 = parseInstagramJSON(json2);
  
  result2.slice(0, 10).forEach((msg, i) => {
    const prefix = msg.sender === 'System' ? '📱 [SYSTEM]' : `${msg.sender}:`;
    console.log(`${i+1}. [${msg.timestamp.toLocaleString()}] ${prefix}`);
    console.log(`   ${msg.text} (isSystem: ${msg.isSystemMessage})`);
    console.log();
  });
} catch (error) {
  console.error('Error testing message_1 3.json:', error.message);
}

console.log('\n=== Test completed! ===');