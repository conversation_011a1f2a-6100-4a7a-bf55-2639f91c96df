// Simple test to verify Instagram parser system message fix
const { parseInstagramJSON } = require('./src/parser.js');

const testData = {
  "participants": [
    {"name": "User1"},
    {"name": "User2"}
  ],
  "messages": [
    {
      "sender_name": "User1",
      "timestamp_ms": 1746065216280,
      "content": "User1 sent an attachment.",
      "is_geoblocked_for_viewer": false,
      "is_unsent_image_by_messenger_kid_parent": false
    },
    {
      "sender_name": "User2", 
      "timestamp_ms": 1746065235200,
      "content": "user2 liked a message",
      "is_geoblocked_for_viewer": false,
      "is_unsent_image_by_messenger_kid_parent": false
    },
    {
      "sender_name": "User1",
      "timestamp_ms": 1746065239600,
      "content": "You sent an attachment.",
      "is_geoblocked_for_viewer": false,
      "is_unsent_image_by_messenger_kid_parent": false
    },
    {
      "sender_name": "User1",
      "timestamp_ms": 1746065240000,
      "content": "Normal message here",
      "is_geoblocked_for_viewer": false,
      "is_unsent_image_by_messenger_kid_parent": false
    }
  ]
};

try {
  const result = parseInstagramJSON(JSON.stringify(testData));
  
  console.log('Test Results:');
  result.forEach((msg, i) => {
    console.log(`${i + 1}. "${msg.text}"`);
    console.log(`   Sender: ${msg.sender}`);
    console.log(`   Is System: ${msg.isSystemMessage}`);
    console.log('');
  });
  
  // Validation
  const checks = [
    { desc: '"sent an attachment" keeps original sender', pass: result[0].sender === 'User1' && result[0].isSystemMessage },
    { desc: '"liked a message" keeps original sender', pass: result[1].sender === 'User2' && result[1].isSystemMessage },
    { desc: '"You sent" changes to System sender', pass: result[2].sender === 'System' && result[2].isSystemMessage },
    { desc: 'Normal message not system', pass: result[3].sender === 'User1' && !result[3].isSystemMessage }
  ];
  
  console.log('Validation:');
  checks.forEach(check => {
    console.log(`${check.pass ? '✅' : '❌'} ${check.desc}`);
  });
  
} catch (error) {
  console.error('Error:', error.message);
}