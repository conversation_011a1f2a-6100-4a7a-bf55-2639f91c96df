import { UnicodeReverter } from './unicode-reverter';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Test suite for Unicode Reverter
 * Tests various Unicode escape scenarios including Turkish, Arabic, emojis
 */

interface TestCase {
  name: string;
  input: string;
  expected: string;
  description: string;
}

class UnicodeReverterTester {
  private testCases: TestCase[] = [
    {
      name: 'Turkish Characters',
      input: 'Bora \\u00c3\\u0096lmez',
      expected: 'Bora Ölmez',
      description: 'Turkish Ö character'
    },
    {
      name: 'Turkish Characters 2',
      input: '\\u00c3\\u00b6mer',
      expected: 'ömer',
      description: 'Turkish ö character'
    },
    {
      name: 'Turkish Text',
      input: 'Bakas\\u00c4\\u00b1m gelmiyor',
      expected: 'Bakasım gelmiyor',
      description: 'Turkish ı character'
    },
    {
      name: 'Turkish Complex',
      input: 'Abi yava\\u00c5\\u009f \\u00c3\\u00a7al\\u00c4\\u00b1\\u00c5\\u009f\\u00c4\\u00b1yor yeni telefon',
      expected: 'Abi yavaş çalışıyor yeni telefon',
      description: 'Multiple Turkish characters'
    },
    {
      name: 'Emoji Laughing',
      input: '\\u00f0\\u009f\\u0098\\u0082',
      expected: '😂',
      description: 'Laughing emoji'
    },
    {
      name: 'Emoji Heart',
      input: '\\u00e2\\u009d\\u00a4',
      expected: '❤',
      description: 'Heart emoji'
    },
    {
      name: 'Emoji Complex',
      input: '\\u00f0\\u009f\\u0098\\u00ad\\u00f0\\u009f\\u0098\\u00ad\\u00f0\\u009f\\u0098\\u00ad\\u00f0\\u009f\\u0098\\u00ad',
      expected: '😭😭😭😭',
      description: 'Multiple crying emojis'
    },
    {
      name: 'Mixed Content',
      input: 'Kayseri\'nin kedisi bile farkl\\u00c4\\u00b1 \\u00f0\\u009f\\u0098\\u0087',
      expected: 'Kayseri\'nin kedisi bile farklı 😇',
      description: 'Turkish text with emoji'
    },
    {
      name: 'Arabic/Kurdish',
      input: 'Strana Michael Jackson stir\\u00c3\\u00ae \\u00c3\\u00bb wek\\u00c3\\u00ae w\\u00c3\\u00ae reqis\\u00c3\\u00ae',
      expected: 'Strana Michael Jackson stirî » wekî wî reqisî',
      description: 'Kurdish text with special characters'
    },
    {
      name: 'Special Quotes',
      input: '\\u00e2\\u0080\\u009cStay with me now\\u00e2\\u0080\\u009d',
      expected: '"Stay with me now"',
      description: 'Smart quotes'
    }
  ];

  /**
   * Creates a test JSON file with Unicode escapes
   */
  private createTestFile(): string {
    const testData = {
      participants: [
        { name: 'Bora \\u00c3\\u0096lmez' },
        { name: '\\u00c3\\u00b6mer' },
        { name: 'Normal Name' }
      ],
      messages: [
        {
          sender_name: 'Bora \\u00c3\\u0096lmez',
          content: 'Bakas\\u00c4\\u00b1m gelmiyor',
          timestamp_ms: 1746065239600
        },
        {
          sender_name: '\\u00c3\\u00b6mer',
          content: '\\u00f0\\u009f\\u0098\\u0082\\u00f0\\u009f\\u0098\\u0082\\u00f0\\u009f\\u0098\\u0082',
          timestamp_ms: 1746065235200
        },
        {
          sender_name: 'Normal Name',
          content: 'Regular English text',
          timestamp_ms: 1746065216280
        }
      ],
      title: '\\u00c5\\u009eahin nane yerkeneeee\\u00f0\\u009f\\u00a5\\u00b6',
      metadata: {
        description: 'Test file with Turkish: \\u00c3\\u00a7al\\u00c4\\u00b1\\u00c5\\u009f\\u00c4\\u00b1yor and emoji: \\u00f0\\u009f\\u0098\\u0087'
      }
    };

    const testFilePath = 'test-unicode-input.json';
    fs.writeFileSync(testFilePath, JSON.stringify(testData, null, 2), 'utf8');
    return testFilePath;
  }

  /**
   * Tests individual Unicode conversion cases
   */
  private testUnicodeConversion(): void {
    console.log('🧪 Testing Unicode Conversion Cases...\n');
    
    let passed = 0;
    let failed = 0;

    for (const testCase of this.testCases) {
      try {
        const reverter = new UnicodeReverter({ inputFile: '' });
        // Access private method for testing
        const result = (reverter as any).convertUnicodeEscapes(testCase.input);
        
        if (result === testCase.expected) {
          console.log(`✅ ${testCase.name}: PASSED`);
          console.log(`   Input:    "${testCase.input}"`);
          console.log(`   Expected: "${testCase.expected}"`);
          console.log(`   Got:      "${result}"`);
          console.log(`   Desc:     ${testCase.description}\n`);
          passed++;
        } else {
          console.log(`❌ ${testCase.name}: FAILED`);
          console.log(`   Input:    "${testCase.input}"`);
          console.log(`   Expected: "${testCase.expected}"`);
          console.log(`   Got:      "${result}"`);
          console.log(`   Desc:     ${testCase.description}\n`);
          failed++;
        }
      } catch (error) {
        console.log(`💥 ${testCase.name}: ERROR - ${error}\n`);
        failed++;
      }
    }

    console.log(`📊 Test Results: ${passed} passed, ${failed} failed\n`);
  }

  /**
   * Tests full file processing
   */
  private async testFileProcessing(): Promise<void> {
    console.log('📁 Testing File Processing...\n');

    // Create test file
    const testInputFile = this.createTestFile();
    const testOutputFile = 'test-unicode-output.json';

    try {
      // Process the file
      const reverter = new UnicodeReverter({
        inputFile: testInputFile,
        outputFile: testOutputFile,
        verbose: true
      });

      await reverter.processFile();

      // Verify output file exists
      if (!fs.existsSync(testOutputFile)) {
        throw new Error('Output file was not created');
      }

      // Read and parse output
      const outputContent = fs.readFileSync(testOutputFile, 'utf8');
      const outputData = JSON.parse(outputContent);

      // Verify some key conversions
      const checks = [
        {
          path: 'participants[0].name',
          expected: 'Bora Ölmez',
          actual: outputData.participants[0].name
        },
        {
          path: 'participants[1].name', 
          expected: 'ömer',
          actual: outputData.participants[1].name
        },
        {
          path: 'messages[0].content',
          expected: 'Bakasım gelmiyor',
          actual: outputData.messages[0].content
        },
        {
          path: 'messages[1].content',
          expected: '😂😂😂',
          actual: outputData.messages[1].content
        },
        {
          path: 'title',
          expected: 'Şahin nane yerkeneeee🥶',
          actual: outputData.title
        }
      ];

      let fileTestsPassed = 0;
      let fileTestsFailed = 0;

      for (const check of checks) {
        if (check.actual === check.expected) {
          console.log(`✅ ${check.path}: "${check.actual}"`);
          fileTestsPassed++;
        } else {
          console.log(`❌ ${check.path}: Expected "${check.expected}", got "${check.actual}"`);
          fileTestsFailed++;
        }
      }

      console.log(`\n📊 File Processing Results: ${fileTestsPassed} passed, ${fileTestsFailed} failed\n`);

      // Cleanup test files
      fs.unlinkSync(testInputFile);
      fs.unlinkSync(testOutputFile);

    } catch (error) {
      console.error(`💥 File processing test failed: ${error}`);
      
      // Cleanup on error
      if (fs.existsSync(testInputFile)) fs.unlinkSync(testInputFile);
      if (fs.existsSync(testOutputFile)) fs.unlinkSync(testOutputFile);
    }
  }

  /**
   * Tests the actual message_1 3.json file
   */
  private async testRealFile(): Promise<void> {
    console.log('🎯 Testing Real Instagram File...\n');

    const realFile = 'message_1 3.json';
    
    if (!fs.existsSync(realFile)) {
      console.log(`⚠️  Real file ${realFile} not found, skipping real file test`);
      return;
    }

    try {
      const reverter = new UnicodeReverter({
        inputFile: realFile,
        outputFile: 'message_1_3_converted.json',
        verbose: true
      });

      await reverter.processFile();
      console.log('✅ Real file processing completed successfully!');

    } catch (error) {
      console.error(`💥 Real file processing failed: ${error}`);
    }
  }

  /**
   * Runs all tests
   */
  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Unicode Reverter Tests\n');
    console.log('=' .repeat(50));
    
    // Test individual conversions
    this.testUnicodeConversion();
    
    // Test file processing
    await this.testFileProcessing();
    
    // Test real file
    await this.testRealFile();
    
    console.log('=' .repeat(50));
    console.log('🏁 All tests completed!');
  }
}

// Run tests when executed directly
if (require.main === module) {
  const tester = new UnicodeReverterTester();
  tester.runAllTests()
    .then(() => {
      console.log('\n🎉 Test suite finished!');
    })
    .catch((error) => {
      console.error('\n💥 Test suite failed:', error);
      process.exit(1);
    });
}

export { UnicodeReverterTester };
